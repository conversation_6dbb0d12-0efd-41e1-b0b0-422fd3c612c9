from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.popup import Popup
from kivy.uix.spinner import Spinner
from kivy.core.window import Window
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.lang import Builder
from kivy.properties import ObjectProperty, StringProperty
from kivy.clock import Clock
import sqlite3
import json
from datetime import datetime
import os

# تعيين اتجاه النص من اليمين لليسار
Window.softinput_mode = 'below_target'

# تعريف واجهة المستخدم
Builder.load_string('''
#:kivy 2.0.0

<LoginScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 20
        spacing: 20
        
        Label:
            text: 'محلات أبو علاء'
            font_size: '24sp'
            size_hint_y: None
            height: '50dp'
            color: 0.2, 0.8, 0.2, 1
        
        Label:
            text: 'نظام إدارة الزبائن'
            font_size: '16sp'
            size_hint_y: None
            height: '30dp'
        
        TextInput:
            id: username
            hint_text: 'اسم المستخدم'
            multiline: False
            size_hint_y: None
            height: '50dp'
            font_size: '16sp'
        
        TextInput:
            id: password
            hint_text: 'كلمة المرور'
            multiline: False
            password: True
            size_hint_y: None
            height: '50dp'
            font_size: '16sp'
        
        Button:
            text: 'تسجيل الدخول'
            size_hint_y: None
            height: '50dp'
            background_color: 0.2, 0.8, 0.2, 1
            on_press: root.login()
        
        Button:
            text: 'إنشاء حساب جديد'
            size_hint_y: None
            height: '50dp'
            background_color: 0.3, 0.3, 0.3, 1
            on_press: root.show_register()

<DashboardScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 10
        
        Label:
            text: 'لوحة التحكم'
            font_size: '20sp'
            size_hint_y: None
            height: '40dp'
            color: 0.2, 0.8, 0.2, 1
        
        GridLayout:
            cols: 2
            spacing: 10
            padding: 10
            
            Button:
                text: 'إضافة زبون'
                background_color: 0.2, 0.8, 0.2, 1
                on_press: root.show_add_customer()
            
            Button:
                text: 'عرض الزبائن'
                background_color: 0.2, 0.6, 0.8, 1
                on_press: root.show_customers()
            
            Button:
                text: 'إضافة معاملة'
                background_color: 0.8, 0.6, 0.2, 1
                on_press: root.show_add_transaction()
            
            Button:
                text: 'التقارير'
                background_color: 0.8, 0.2, 0.6, 1
                on_press: root.show_reports()
            
            Button:
                text: 'الإعدادات'
                background_color: 0.6, 0.6, 0.6, 1
                on_press: root.show_settings()
            
            Button:
                text: 'تسجيل الخروج'
                background_color: 0.8, 0.2, 0.2, 1
                on_press: root.logout()

<AddCustomerScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 10
        
        Label:
            text: 'إضافة زبون جديد'
            font_size: '18sp'
            size_hint_y: None
            height: '40dp'
            color: 0.2, 0.8, 0.2, 1
        
        ScrollView:
            GridLayout:
                cols: 1
                spacing: 10
                size_hint_y: None
                height: self.minimum_height
                padding: 10
                
                TextInput:
                    id: customer_name
                    hint_text: 'اسم الزبون *'
                    multiline: False
                    size_hint_y: None
                    height: '50dp'
                
                TextInput:
                    id: customer_phone
                    hint_text: 'رقم الهاتف *'
                    multiline: False
                    input_type: 'phone'
                    size_hint_y: None
                    height: '50dp'
                
                TextInput:
                    id: customer_email
                    hint_text: 'البريد الإلكتروني'
                    multiline: False
                    input_type: 'email'
                    size_hint_y: None
                    height: '50dp'
                
                Spinner:
                    id: customer_category
                    text: 'عادي'
                    values: ['عادي', 'VIP', 'مميز', 'جديد', 'قديم']
                    size_hint_y: None
                    height: '50dp'
                
                TextInput:
                    id: customer_notes
                    hint_text: 'ملاحظات'
                    multiline: True
                    size_hint_y: None
                    height: '100dp'
        
        BoxLayout:
            size_hint_y: None
            height: '50dp'
            spacing: 10
            
            Button:
                text: 'إلغاء'
                background_color: 0.6, 0.6, 0.6, 1
                on_press: root.cancel()
            
            Button:
                text: 'حفظ'
                background_color: 0.2, 0.8, 0.2, 1
                on_press: root.save_customer()

<CustomersScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 10
        
        Label:
            text: 'قائمة الزبائن'
            font_size: '18sp'
            size_hint_y: None
            height: '40dp'
            color: 0.2, 0.8, 0.2, 1
        
        TextInput:
            id: search_input
            hint_text: 'البحث في الزبائن...'
            multiline: False
            size_hint_y: None
            height: '40dp'
            on_text: root.search_customers()
        
        ScrollView:
            GridLayout:
                id: customers_list
                cols: 1
                spacing: 5
                size_hint_y: None
                height: self.minimum_height
                padding: 10

<AddTransactionScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 10
        
        Label:
            text: 'إضافة معاملة'
            font_size: '18sp'
            size_hint_y: None
            height: '40dp'
            color: 0.2, 0.8, 0.2, 1
        
        GridLayout:
            cols: 1
            spacing: 10
            padding: 10
            
            Spinner:
                id: transaction_customer
                text: 'اختر الزبون'
                values: []
                size_hint_y: None
                height: '50dp'
            
            Spinner:
                id: transaction_type
                text: 'بيع'
                values: ['بيع', 'تسديد']
                size_hint_y: None
                height: '50dp'
            
            TextInput:
                id: transaction_amount
                hint_text: 'المبلغ'
                multiline: False
                input_type: 'number'
                size_hint_y: None
                height: '50dp'
            
            Spinner:
                id: payment_method
                text: 'نقداً'
                values: ['نقداً', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان']
                size_hint_y: None
                height: '50dp'
            
            TextInput:
                id: transaction_notes
                hint_text: 'ملاحظات'
                multiline: True
                size_hint_y: None
                height: '100dp'
        
        BoxLayout:
            size_hint_y: None
            height: '50dp'
            spacing: 10
            
            Button:
                text: 'إلغاء'
                background_color: 0.6, 0.6, 0.6, 1
                on_press: root.cancel()
            
            Button:
                text: 'حفظ'
                background_color: 0.2, 0.8, 0.2, 1
                on_press: root.save_transaction()
''')

class Database:
    def __init__(self):
        self.db_path = 'crm_android.db'
        self.init_database()
    
    def init_database(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول الزبائن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT UNIQUE NOT NULL,
                email TEXT,
                category TEXT DEFAULT 'عادي',
                status TEXT DEFAULT 'نشط',
                total_balance REAL DEFAULT 0.0,
                paid_amount REAL DEFAULT 0.0,
                pending_amount REAL DEFAULT 0.0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المعاملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                payment_method TEXT DEFAULT 'نقداً',
                notes TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        # إنشاء مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, role)
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        conn.commit()
        conn.close()

class LoginScreen(Screen):
    def login(self):
        username = self.ids.username.text
        password = self.ids.password.text
        
        if username == 'admin' and password == 'admin123':
            self.manager.current = 'dashboard'
        else:
            self.show_error('بيانات الدخول غير صحيحة')
    
    def show_register(self):
        self.show_error('سيتم إضافة هذه الميزة قريباً')
    
    def show_error(self, message):
        popup = Popup(title='خطأ', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()

class DashboardScreen(Screen):
    def show_add_customer(self):
        self.manager.current = 'add_customer'
    
    def show_customers(self):
        self.manager.current = 'customers'
    
    def show_add_transaction(self):
        self.manager.current = 'add_transaction'
    
    def show_reports(self):
        self.show_message('سيتم إضافة التقارير قريباً')
    
    def show_settings(self):
        self.show_message('سيتم إضافة الإعدادات قريباً')
    
    def logout(self):
        self.manager.current = 'login'
    
    def show_message(self, message):
        popup = Popup(title='رسالة', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()

class AddCustomerScreen(Screen):
    def save_customer(self):
        name = self.ids.customer_name.text
        phone = self.ids.customer_phone.text
        email = self.ids.customer_email.text
        category = self.ids.customer_category.text
        notes = self.ids.customer_notes.text
        
        if not name or not phone:
            self.show_error('يرجى ملء الحقول المطلوبة')
            return
        
        db = Database()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO customers (name, phone, email, category, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, email, category, notes))
            conn.commit()
            self.show_success('تم إضافة الزبون بنجاح')
            self.clear_form()
        except sqlite3.IntegrityError:
            self.show_error('رقم الهاتف موجود مسبقاً')
        finally:
            conn.close()
    
    def clear_form(self):
        self.ids.customer_name.text = ''
        self.ids.customer_phone.text = ''
        self.ids.customer_email.text = ''
        self.ids.customer_notes.text = ''
    
    def cancel(self):
        self.manager.current = 'dashboard'
    
    def show_error(self, message):
        popup = Popup(title='خطأ', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()
    
    def show_success(self, message):
        popup = Popup(title='نجح', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()

class CustomersScreen(Screen):
    def on_enter(self):
        self.load_customers()
    
    def load_customers(self):
        db = Database()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, phone, category, pending_amount FROM customers ORDER BY created_at DESC')
        customers = cursor.fetchall()
        conn.close()
        
        self.ids.customers_list.clear_widgets()
        
        for customer in customers:
            customer_id, name, phone, category, pending = customer
            layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='80dp')
            
            info_layout = BoxLayout(orientation='vertical')
            info_layout.add_widget(Label(text=name, size_hint_y=None, height='30dp'))
            info_layout.add_widget(Label(text=phone, size_hint_y=None, height='25dp'))
            info_layout.add_widget(Label(text=f'{category} - {pending} د.ع', size_hint_y=None, height='25dp'))
            
            layout.add_widget(info_layout)
            
            # أزرار الإجراءات
            actions_layout = BoxLayout(orientation='vertical', size_hint_x=0.3)
            actions_layout.add_widget(Button(text='تعديل', size_hint_y=None, height='35dp'))
            actions_layout.add_widget(Button(text='حذف', size_hint_y=None, height='35dp', background_color=(0.8, 0.2, 0.2, 1)))
            
            layout.add_widget(actions_layout)
            self.ids.customers_list.add_widget(layout)
    
    def search_customers(self):
        # سيتم تنفيذ البحث لاحقاً
        pass

class AddTransactionScreen(Screen):
    def on_enter(self):
        self.load_customers()
    
    def load_customers(self):
        db = Database()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, phone FROM customers ORDER BY name')
        customers = cursor.fetchall()
        conn.close()
        
        customer_values = [f'{name} - {phone}' for _, name, phone in customers]
        self.ids.transaction_customer.values = customer_values
        if customer_values:
            self.ids.transaction_customer.text = customer_values[0]
    
    def save_transaction(self):
        customer_text = self.ids.transaction_customer.text
        transaction_type = self.ids.transaction_type.text
        amount_text = self.ids.transaction_amount.text
        payment_method = self.ids.payment_method.text
        notes = self.ids.transaction_notes.text
        
        if not customer_text or not amount_text:
            self.show_error('يرجى ملء الحقول المطلوبة')
            return
        
        try:
            amount = float(amount_text)
        except ValueError:
            self.show_error('يرجى إدخال مبلغ صحيح')
            return
        
        # استخراج معرف الزبون من النص
        customer_name = customer_text.split(' - ')[0]
        
        db = Database()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        try:
            # الحصول على معرف الزبون
            cursor.execute('SELECT id FROM customers WHERE name = ?', (customer_name,))
            customer_result = cursor.fetchone()
            
            if not customer_result:
                self.show_error('الزبون غير موجود')
                return
            
            customer_id = customer_result[0]
            
            # إضافة المعاملة
            cursor.execute('''
                INSERT INTO transactions (customer_id, transaction_type, amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (customer_id, transaction_type, amount, payment_method, notes))
            
            # تحديث رصيد الزبون
            if transaction_type == 'بيع':
                cursor.execute('''
                    UPDATE customers 
                    SET total_balance = total_balance + ?, 
                        pending_amount = total_balance - paid_amount
                    WHERE id = ?
                ''', (amount, customer_id))
            else:  # تسديد
                cursor.execute('''
                    UPDATE customers 
                    SET paid_amount = paid_amount + ?, 
                        pending_amount = total_balance - paid_amount
                    WHERE id = ?
                ''', (amount, customer_id))
            
            conn.commit()
            self.show_success('تم إضافة المعاملة بنجاح')
            self.clear_form()
        except Exception as e:
            self.show_error(f'حدث خطأ: {str(e)}')
        finally:
            conn.close()
    
    def clear_form(self):
        self.ids.transaction_amount.text = ''
        self.ids.transaction_notes.text = ''
    
    def cancel(self):
        self.manager.current = 'dashboard'
    
    def show_error(self, message):
        popup = Popup(title='خطأ', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()
    
    def show_success(self, message):
        popup = Popup(title='نجح', content=Label(text=message), size_hint=(None, None), size=(300, 200))
        popup.open()

class AbuAlaaCRMApp(App):
    def build(self):
        # تهيئة قاعدة البيانات
        Database()
        
        # إنشاء مدير الشاشات
        sm = ScreenManager()
        sm.add_widget(LoginScreen(name='login'))
        sm.add_widget(DashboardScreen(name='dashboard'))
        sm.add_widget(AddCustomerScreen(name='add_customer'))
        sm.add_widget(CustomersScreen(name='customers'))
        sm.add_widget(AddTransactionScreen(name='add_transaction'))
        
        return sm

if __name__ == '__main__':
    AbuAlaaCRMApp().run() 