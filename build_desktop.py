#!/usr/bin/env python3
"""
Build script for Abu Alaa Stores CRM Desktop Application
Creates both console and windowed versions
"""

import PyInstaller.__main__
import os
import shutil
import sys

def build_desktop_app():
    """Build the desktop application with GUI launcher"""
    
    print("🚀 Building Abu Alaa Stores CRM Desktop Application")
    print("=" * 60)
    
    # Configuration
    launcher_file = 'desktop_launcher.py'
    console_file = 'app.py'
    app_name = 'AbuAlaaStoresCRM'
    build_dir = 'dist_desktop'
    work_dir = 'build_desktop'
    
    # Clean previous builds
    if os.path.exists(build_dir):
        print("🧹 Cleaning previous build...")
        shutil.rmtree(build_dir)
    if os.path.exists(work_dir):
        shutil.rmtree(work_dir)
    
    # Create necessary directories
    os.makedirs('uploads', exist_ok=True)
    
    # Build GUI version (windowed)
    print("\n📱 Building GUI Desktop Version...")
    gui_options = [
        launcher_file,
        '--onefile',
        '--windowed',  # No console window
        f'--name={app_name}_Desktop',
        f'--distpath={build_dir}',
        f'--workpath={work_dir}',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=uploads;uploads',
        '--add-data=app.py;.',
        # Hidden imports
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=werkzeug.security',
        '--hidden-import=jinja2',
        '--hidden-import=openpyxl',
        '--hidden-import=reportlab',
        '--hidden-import=qrcode',
        '--hidden-import=PIL',
        '--hidden-import=bcrypt',
        '--hidden-import=jwt',
        '--hidden-import=tkinter',
        '--hidden-import=pystray',
        '--hidden-import=requests',
        '--clean',
        '--noconfirm',
    ]
    
    # Build Console version
    print("\n💻 Building Console Version...")
    console_options = [
        console_file,
        '--onefile',
        '--console',  # Keep console window
        f'--name={app_name}_Console',
        f'--distpath={build_dir}',
        f'--workpath={work_dir}',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=uploads;uploads',
        # Hidden imports
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=werkzeug.security',
        '--hidden-import=jinja2',
        '--hidden-import=openpyxl',
        '--hidden-import=reportlab',
        '--hidden-import=qrcode',
        '--hidden-import=PIL',
        '--hidden-import=bcrypt',
        '--hidden-import=jwt',
        '--clean',
        '--noconfirm',
    ]
    
    try:
        # Build GUI version
        print("⚙️  Building GUI version...")
        PyInstaller.__main__.run(gui_options)
        
        # Build Console version
        print("⚙️  Building Console version...")
        PyInstaller.__main__.run(console_options)
        
        # Post-build setup
        setup_distribution(build_dir, app_name)
        
        print(f"\n✅ Build completed successfully!")
        print(f"📁 Distribution folder: {os.path.abspath(build_dir)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def setup_distribution(build_dir, app_name):
    """Setup the distribution folder with all necessary files"""
    
    # Create batch files for easy launching
    create_batch_files(build_dir, app_name)
    
    # Create comprehensive README
    create_comprehensive_readme(build_dir)
    
    # Create sample data folder
    sample_dir = os.path.join(build_dir, 'sample_data')
    os.makedirs(sample_dir, exist_ok=True)
    
    print("📝 Created distribution files")

def create_batch_files(build_dir, app_name):
    """Create batch files for launching the application"""
    
    # GUI launcher batch file
    gui_batch = f'''@echo off
title Abu Alaa Stores CRM - Desktop
echo Starting Abu Alaa Stores CRM Desktop Application...
echo.
echo This will open the application with a modern GUI interface.
echo The application will minimize to system tray when closed.
echo.
"{app_name}_Desktop.exe"
'''
    
    # Console launcher batch file
    console_batch = f'''@echo off
title Abu Alaa Stores CRM - Console
echo Starting Abu Alaa Stores CRM Console Application...
echo.
echo This version shows detailed logging information.
echo Keep this window open while using the application.
echo.
"{app_name}_Console.exe"
pause
'''
    
    # Write batch files
    with open(os.path.join(build_dir, 'Start_Desktop_GUI.bat'), 'w') as f:
        f.write(gui_batch)
    
    with open(os.path.join(build_dir, 'Start_Console_Mode.bat'), 'w') as f:
        f.write(console_batch)

def create_comprehensive_readme(build_dir):
    """Create a comprehensive README file"""
    readme_content = '''# Abu Alaa Stores CRM Desktop Application

## Quick Start Guide

### Option 1: Desktop GUI Mode (Recommended)
- Double-click "Start_Desktop_GUI.bat" or "AbuAlaaStoresCRM_Desktop.exe"
- Modern GUI interface with system tray support
- Application minimizes to tray when closed
- Best user experience

### Option 2: Console Mode (For Troubleshooting)
- Double-click "Start_Console_Mode.bat" or "AbuAlaaStoresCRM_Console.exe"
- Shows detailed logging information
- Useful for debugging issues
- Keep console window open while using

## First Time Setup
1. Launch the application using either method above
2. The application will automatically create a local database
3. Open your web browser and go to the displayed URL (usually http://127.0.0.1:5000)
4. Login with default credentials: admin / admin123
5. Change the admin password after first login

## Features
- 👥 Customer Management - Add, edit, and organize customer information
- 💰 Transaction Tracking - Record sales and payments
- 📊 Financial Reports - Generate Excel and PDF reports
- 📁 Document Management - Upload and organize customer documents
- 📞 Communication Logs - Track calls, emails, and meetings
- ⏰ Reminders - Set follow-up reminders for customers
- 🔒 User Management - Multiple user accounts with role-based access

## System Requirements
- Windows 7 or later (64-bit recommended)
- 4GB RAM minimum (8GB recommended)
- 200MB free disk space
- Internet connection (for initial setup only)

## Data Storage
- All data is stored locally on your computer
- Database file: crm.db (created automatically)
- Uploaded files: uploads/ folder
- Log files: app.log (in console mode)

## Troubleshooting

### Application won't start
1. Try running in Console Mode to see error messages
2. Check if antivirus software is blocking the application
3. Ensure you have administrator privileges
4. Try running as administrator (right-click → Run as administrator)

### Browser doesn't open automatically
1. Manually open your web browser
2. Go to http://127.0.0.1:5000 (or the URL shown in console)
3. If port 5000 is busy, the app will use a different port

### Database issues
1. Close the application completely
2. Delete the crm.db file (this will reset all data)
3. Restart the application to create a fresh database

### Performance issues
1. Close unnecessary programs to free up memory
2. Restart the application periodically
3. Use Console Mode for better performance monitoring

## Security Notes
- The application runs locally and doesn't send data over the internet
- Change default admin password immediately after first login
- Regular backups of crm.db file are recommended
- Keep the application updated for security patches

## Support
For technical support or feature requests, please contact the development team.

## Version Information
Desktop Application v2.0
Built with Flask + PyInstaller + Tkinter
Last updated: 2024

## License
This software is proprietary to Abu Alaa Stores.
Unauthorized distribution is prohibited.
'''
    
    with open(os.path.join(build_dir, 'README.txt'), 'w', encoding='utf-8') as f:
        f.write(readme_content)

def main():
    """Main build function"""
    success = build_desktop_app()
    
    if success:
        print("\n🎉 Build process completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Test both GUI and Console versions")
        print("2. Create installer package (optional)")
        print("3. Distribute the dist_desktop folder to users")
    else:
        print("\n💥 Build process failed!")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
