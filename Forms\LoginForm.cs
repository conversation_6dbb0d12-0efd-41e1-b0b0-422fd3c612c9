using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using CRMDesktopApp.Models;
using CRMDesktopApp.Services;
using Microsoft.Extensions.DependencyInjection;

namespace CRMDesktopApp.Forms
{
    public partial class LoginForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnExit;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private PictureBox picLogo;
        private Panel pnlMain;
        private CheckBox chkRememberMe;

        public User? LoggedInUser { get; private set; }

        public LoginForm(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "تسجيل الدخول - نظام إدارة الزبائن";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Main panel
            pnlMain = new Panel
            {
                Size = new Size(400, 300),
                Location = new Point(25, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(64, 64),
                Location = new Point(168, 20),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.LightBlue
            };

            // Title
            lblTitle = new Label
            {
                Text = "محلات أبو علاء\nنظام إدارة الزبائن",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(300, 50),
                Location = new Point(50, 90)
            };

            // Username label
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10),
                Size = new Size(100, 23),
                Location = new Point(280, 160),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Username textbox
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(200, 23),
                Location = new Point(70, 160),
                Text = "admin"
            };

            // Password label
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10),
                Size = new Size(100, 23),
                Location = new Point(280, 195),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Password textbox
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(200, 23),
                Location = new Point(70, 195),
                UseSystemPasswordChar = true,
                Text = "admin123"
            };

            // Remember me checkbox
            chkRememberMe = new CheckBox
            {
                Text = "تذكرني",
                Font = new Font("Tahoma", 9),
                Size = new Size(80, 20),
                Location = new Point(190, 225),
                TextAlign = ContentAlignment.MiddleRight,
                CheckAlign = ContentAlignment.MiddleLeft
            };

            // Login button
            btnLogin = new Button
            {
                Text = "دخول",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(90, 35),
                Location = new Point(180, 250),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Exit button
            btnExit = new Button
            {
                Text = "خروج",
                Font = new Font("Tahoma", 10),
                Size = new Size(90, 35),
                Location = new Point(80, 250),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Add controls to panel
            pnlMain.Controls.AddRange(new Control[]
            {
                picLogo, lblTitle, lblUsername, txtUsername,
                lblPassword, txtPassword, chkRememberMe, btnLogin, btnExit
            });

            // Add panel to form
            this.Controls.Add(pnlMain);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Event handlers
            btnLogin.Click += BtnLogin_Click;
            btnExit.Click += BtnExit_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.KeyPress += TxtUsername_KeyPress;

            // Set button styles
            SetButtonStyle(btnLogin, Color.FromArgb(0, 123, 255));
            SetButtonStyle(btnExit, Color.FromArgb(108, 117, 125));

            // Add shadow effect to main panel
            AddShadowEffect();

            // Focus on username
            txtUsername.Focus();
        }

        private void SetButtonStyle(Button button, Color backColor)
        {
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backColor, 0.1f);
        }

        private void AddShadowEffect()
        {
            // Simple shadow effect by adding a darker panel behind
            var shadowPanel = new Panel
            {
                Size = new Size(pnlMain.Width + 5, pnlMain.Height + 5),
                Location = new Point(pnlMain.Location.X + 3, pnlMain.Location.Y + 3),
                BackColor = Color.FromArgb(50, 0, 0, 0)
            };
            this.Controls.Add(shadowPanel);
            shadowPanel.SendToBack();
        }

        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await LoginAsync();
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private async void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                await LoginAsync();
            }
        }

        private async void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private async Task LoginAsync()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Disable controls during login
            SetControlsEnabled(false);
            btnLogin.Text = "جاري التحقق...";

            try
            {
                var user = await _databaseService.AuthenticateUserAsync(txtUsername.Text.Trim(), txtPassword.Text);

                if (user != null)
                {
                    LoggedInUser = user;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسجيل الدخول:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                SetControlsEnabled(true);
                btnLogin.Text = "دخول";
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnExit.Enabled = enabled;
            chkRememberMe.Enabled = enabled;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // Initialize database on form load
            Task.Run(async () =>
            {
                try
                {
                    await _databaseService.InitializeDatabaseAsync();
                }
                catch (Exception ex)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"فشل في تهيئة قاعدة البيانات:\n{ex.Message}", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
            });
        }
    }
}
