#!/usr/bin/env python3
"""
إنشاء ملف تنصيب بسيط بدون الحاجة لبرامج إضافية
ينشئ ملف batch للتنصيب التلقائي
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_simple_installer():
    """إنشاء ملف تنصيب بسيط"""
    print("🔧 إنشاء ملف التنصيب البسيط...")
    
    # إنشاء مجلد التنصيب
    installer_dir = "simple_installer"
    if os.path.exists(installer_dir):
        shutil.rmtree(installer_dir)
    os.makedirs(installer_dir)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        "dist/AbuAlaaStoresCRM.exe",
        "README.md",
        "SETUP_GUIDE.md",
        "LICENSE.txt"
    ]
    
    dirs_to_copy = ["templates", "static", "uploads"]
    
    # نسخ الملفات
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            shutil.copy2(file_path, os.path.join(installer_dir, filename))
    
    # نسخ المجلدات
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, os.path.join(installer_dir, dir_name))
    
    # إنشاء سكريبت التنصيب
    install_script = """@echo off
chcp 65001 >nul
title تثبيت نظام إدارة الزبائن - محلات أبو علاء
color 0A

echo.
echo ========================================
echo    تثبيت نظام إدارة الزبائن
echo        محلات أبو علاء
echo ========================================
echo.
echo الإصدار: 2.0.0
echo التاريخ: """ + datetime.now().strftime("%Y-%m-%d") + """
echo.

echo جاري التحقق من المتطلبات...
timeout /t 2 >nul

REM التحقق من نظام التشغيل
ver | find "Windows" >nul
if errorlevel 1 (
    echo ❌ هذا البرنامج يعمل على Windows فقط
    pause
    exit /b 1
)

echo ✅ نظام التشغيل متوافق

REM إنشاء مجلد التثبيت
set "INSTALL_DIR=%ProgramFiles%\\Abu Alaa Stores CRM"
echo.
echo مجلد التثبيت: %INSTALL_DIR%
echo.

REM طلب صلاحيات المدير
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  يتطلب صلاحيات المدير للتثبيت
    echo يرجى تشغيل الملف كمدير
    pause
    exit /b 1
)

echo ✅ صلاحيات المدير متوفرة

echo.
echo جاري إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo جاري نسخ الملفات...
copy "AbuAlaaStoresCRM.exe" "%INSTALL_DIR%\\" >nul
copy "README.md" "%INSTALL_DIR%\\" >nul
copy "SETUP_GUIDE.md" "%INSTALL_DIR%\\" >nul
copy "LICENSE.txt" "%INSTALL_DIR%\\" >nul

echo جاري نسخ المجلدات...
xcopy "templates" "%INSTALL_DIR%\\templates\\" /E /I /Q >nul
xcopy "static" "%INSTALL_DIR%\\static\\" /E /I /Q >nul
xcopy "uploads" "%INSTALL_DIR%\\uploads\\" /E /I /Q >nul

echo جاري إنشاء الاختصارات...

REM اختصار سطح المكتب
set "DESKTOP=%USERPROFILE%\\Desktop"
echo [InternetShortcut] > "%DESKTOP%\\محلات أبو علاء CRM.url"
echo URL=file:///%INSTALL_DIR%\\AbuAlaaStoresCRM.exe >> "%DESKTOP%\\محلات أبو علاء CRM.url"

REM اختصار قائمة ابدأ
set "STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
if not exist "%STARTMENU%\\محلات أبو علاء" mkdir "%STARTMENU%\\محلات أبو علاء"
echo [InternetShortcut] > "%STARTMENU%\\محلات أبو علاء\\محلات أبو علاء CRM.url"
echo URL=file:///%INSTALL_DIR%\\AbuAlaaStoresCRM.exe >> "%STARTMENU%\\محلات أبو علاء\\محلات أبو علاء CRM.url"

REM إنشاء ملف إلغاء التثبيت
echo @echo off > "%INSTALL_DIR%\\uninstall.bat"
echo title إلغاء تثبيت نظام إدارة الزبائن >> "%INSTALL_DIR%\\uninstall.bat"
echo echo جاري إلغاء التثبيت... >> "%INSTALL_DIR%\\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\\uninstall.bat"
echo del "%DESKTOP%\\محلات أبو علاء CRM.url" >> "%INSTALL_DIR%\\uninstall.bat"
echo rd /s /q "%STARTMENU%\\محلات أبو علاء" >> "%INSTALL_DIR%\\uninstall.bat"
echo echo تم إلغاء التثبيت بنجاح >> "%INSTALL_DIR%\\uninstall.bat"
echo pause >> "%INSTALL_DIR%\\uninstall.bat"

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة ابدأ: تم إضافته
echo.
echo بيانات الدخول الافتراضية:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⚠️  يُرجى تغيير كلمة المرور فور الدخول الأول
echo.

set /p "START=هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%START%"=="y" (
    echo جاري تشغيل البرنامج...
    start "" "%INSTALL_DIR%\\AbuAlaaStoresCRM.exe"
)

echo.
echo شكراً لاختيارك نظام إدارة الزبائن من محلات أبو علاء!
echo.
pause
"""
    
    # حفظ سكريبت التنصيب
    with open(os.path.join(installer_dir, "install.bat"), "w", encoding="utf-8") as f:
        f.write(install_script)
    
    # إنشاء ملف معلومات
    info_text = """نظام إدارة الزبائن - محلات أبو علاء
===================================

مرحباً بك في نظام إدارة الزبائن المتطور!

طريقة التثبيت:
--------------
1. انقر نقراً مزدوجاً على "install.bat"
2. اتبع التعليمات على الشاشة
3. سيتم إنشاء اختصارات تلقائياً

متطلبات النظام:
---------------
• Windows 7 أو أحدث
• 2 جيجابايت RAM
• 500 ميجابايت مساحة فارغة
• صلاحيات المدير للتثبيت

بيانات الدخول الافتراضية:
--------------------------
اسم المستخدم: admin
كلمة المرور: admin123

⚠️ مهم: غيّر كلمة المرور فوراً بعد الدخول!

المميزات:
---------
✅ إدارة شاملة للزبائن
✅ تتبع المبيعات والمدفوعات  
✅ تقارير مالية مفصلة
✅ إدارة المستندات
✅ نظام التذكيرات
✅ واجهة عربية كاملة

للدعم الفني:
------------
راجع ملف SETUP_GUIDE.md للتعليمات المفصلة

© 2024 محلات أبو علاء - جميع الحقوق محفوظة
"""
    
    with open(os.path.join(installer_dir, "اقرأني_أولاً.txt"), "w", encoding="utf-8") as f:
        f.write(info_text)
    
    print(f"✅ تم إنشاء ملف التنصيب البسيط: {os.path.abspath(installer_dir)}")
    return True

def create_auto_extractor():
    """إنشاء ملف تنصيب ذاتي الاستخراج"""
    print("📦 إنشاء ملف التنصيب ذاتي الاستخراج...")
    
    try:
        # إنشاء ملف مضغوط مع سكريبت التنصيب
        zip_filename = "AbuAlaaStoresCRM_Installer.zip"
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة ملفات التنصيب البسيط
            installer_dir = "simple_installer"
            if os.path.exists(installer_dir):
                for root, dirs, files in os.walk(installer_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, installer_dir)
                        zipf.write(file_path, arc_path)
        
        # إنشاء سكريبت استخراج وتنصيب
        extractor_script = """@echo off
chcp 65001 >nul
title استخراج وتثبيت نظام إدارة الزبائن
color 0B

echo.
echo ========================================
echo   استخراج وتثبيت نظام إدارة الزبائن
echo        محلات أبو علاء
echo ========================================
echo.

echo جاري استخراج الملفات...

REM إنشاء مجلد مؤقت
set "TEMP_DIR=%TEMP%\\AbuAlaaStoresCRM_Install"
if exist "%TEMP_DIR%" rd /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

REM استخراج الملفات (يتطلب PowerShell)
powershell -command "Expand-Archive -Path 'AbuAlaaStoresCRM_Installer.zip' -DestinationPath '%TEMP_DIR%' -Force"

if errorlevel 1 (
    echo ❌ فشل في استخراج الملفات
    echo يرجى استخراج الملف يدوياً وتشغيل install.bat
    pause
    exit /b 1
)

echo ✅ تم استخراج الملفات بنجاح

echo جاري بدء التثبيت...
cd /d "%TEMP_DIR%"
call install.bat

echo جاري تنظيف الملفات المؤقتة...
cd /d "%~dp0"
rd /s /q "%TEMP_DIR%"

echo.
echo تم الانتهاء من التثبيت!
pause
"""
        
        with open("AbuAlaaStoresCRM_AutoInstaller.bat", "w", encoding="utf-8") as f:
            f.write(extractor_script)
        
        file_size = os.path.getsize(zip_filename) / (1024 * 1024)
        print(f"✅ ملف التنصيب ذاتي الاستخراج: AbuAlaaStoresCRM_AutoInstaller.bat")
        print(f"✅ ملف البيانات: {zip_filename} ({file_size:.1f} MB)")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف التنصيب ذاتي الاستخراج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملف التنصيب البسيط")
    print("=" * 40)
    
    # التحقق من وجود الملف التنفيذي
    if not os.path.exists("dist/AbuAlaaStoresCRM.exe"):
        print("❌ الملف التنفيذي غير موجود!")
        print("يرجى تشغيل: python build_exe.py أولاً")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء ملف التنصيب البسيط
    simple_success = create_simple_installer()
    
    # إنشاء ملف التنصيب ذاتي الاستخراج
    auto_success = create_auto_extractor()
    
    print("\n" + "=" * 40)
    print("📊 ملخص النتائج")
    print("=" * 40)
    
    if simple_success:
        print("✅ التنصيب البسيط: simple_installer/install.bat")
    
    if auto_success:
        print("✅ التنصيب التلقائي: AbuAlaaStoresCRM_AutoInstaller.bat")
    
    if simple_success or auto_success:
        print("\n🎉 تم إنشاء ملفات التنصيب بنجاح!")
        print("\n📋 طريقة الاستخدام:")
        print("1. للتنصيب البسيط: شغل simple_installer/install.bat كمدير")
        print("2. للتنصيب التلقائي: شغل AbuAlaaStoresCRM_AutoInstaller.bat")
        print("\n⚠️  تأكد من تشغيل ملفات التنصيب بصلاحيات المدير")
    else:
        print("\n💥 فشل في إنشاء ملفات التنصيب")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
