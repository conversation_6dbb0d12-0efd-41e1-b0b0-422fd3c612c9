import PyInstaller.__main__
import os
import sys
import shutil
import subprocess

def build_exe():
    """Build Windows executable with enhanced configuration"""

    print("🚀 Building Abu Alaa Stores CRM Desktop Application...")
    print("=" * 60)

    # Configuration
    main_file = 'app.py'
    app_name = 'AbuAlaaStoresCRM'
    build_dir = 'dist'
    work_dir = 'build'

    # Clean previous builds
    if os.path.exists(build_dir):
        print("🧹 Cleaning previous build...")
        shutil.rmtree(build_dir)
    if os.path.exists(work_dir):
        shutil.rmtree(work_dir)

    # Create necessary directories
    os.makedirs('uploads', exist_ok=True)

    # Enhanced PyInstaller options
    options = [
        main_file,
        '--onefile',  # Single executable file
        '--console',  # Keep console for logging (change to --windowed for no console)
        f'--name={app_name}',
        f'--distpath={build_dir}',
        f'--workpath={work_dir}',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=uploads;uploads',
        # Hidden imports for all dependencies
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=werkzeug.security',
        '--hidden-import=jinja2',
        '--hidden-import=openpyxl',
        '--hidden-import=openpyxl.styles',
        '--hidden-import=reportlab',
        '--hidden-import=reportlab.lib',
        '--hidden-import=reportlab.platypus',
        '--hidden-import=qrcode',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=bcrypt',
        '--hidden-import=jwt',
        '--hidden-import=sqlite3',
        '--hidden-import=threading',
        '--hidden-import=webbrowser',
        '--hidden-import=socket',
        '--hidden-import=logging',
        '--hidden-import=datetime',
        '--hidden-import=uuid',
        '--hidden-import=json',
        '--hidden-import=io',
        '--hidden-import=signal',
        '--hidden-import=atexit',
        # Additional options
        '--clean',
        '--noconfirm',
        '--strip',  # Strip debug symbols
        '--optimize=2',  # Python optimization level
    ]

    print("⚙️  Building executable...")
    print("⏳ This may take several minutes...")

    try:
        # Run PyInstaller
        PyInstaller.__main__.run(options)

        # Post-build setup
        exe_path = os.path.join(build_dir, f'{app_name}.exe')
        if os.path.exists(exe_path):
            print(f"\n✅ Build completed successfully!")
            print(f"📁 Executable location: {os.path.abspath(exe_path)}")

            # Create startup script
            create_startup_script(build_dir, app_name)

            # Create README
            create_readme(build_dir)

            print("\n📋 Important Notes:")
            print("- The application will automatically create a database on first run")
            print("- Default login credentials: admin / admin123")
            print("- The app will open in your default web browser")
            print("- Close the console window to exit the application")
            print(f"- Log files will be created in the same directory as the executable")

            return True
        else:
            print("❌ Build failed - executable not found")
            return False

    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_startup_script(build_dir, app_name):
    """Create a startup batch script"""
    script_content = f'''@echo off
title Abu Alaa Stores CRM
echo Starting Abu Alaa Stores CRM...
echo.
"{app_name}.exe"
pause
'''
    script_path = os.path.join(build_dir, 'Start_CRM.bat')
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    print(f"📝 Created startup script: {script_path}")

def create_readme(build_dir):
    """Create README file"""
    readme_content = '''# Abu Alaa Stores CRM Desktop Application

## Quick Start
1. Double-click on AbuAlaaStoresCRM.exe to start the application
2. The application will open in your web browser automatically
3. Login with: admin / admin123
4. Close the console window to exit

## Features
- Customer Management
- Transaction Tracking
- Payment Processing
- Document Management
- Reports Generation
- Communication Logs

## System Requirements
- Windows 7 or later
- 4GB RAM minimum
- 100MB free disk space

## Support
For technical support, please contact the development team.

## Version
Desktop Application v1.0
Built with Flask + PyInstaller
'''
    readme_path = os.path.join(build_dir, 'README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"📄 Created README: {readme_path}")

if __name__ == "__main__":
    success = build_exe()
    if success:
        print("\n🎉 Build process completed successfully!")
    else:
        print("\n💥 Build process failed!")

    input("\nPress Enter to exit...")