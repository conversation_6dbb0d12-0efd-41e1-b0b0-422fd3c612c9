import PyInstaller.__main__
import os
import sys

def build_exe():
    """بناء ملف تنفيذي للويندوز"""
    
    # تحديد مسار الملف الرئيسي
    main_file = 'app.py'
    
    # تحديد مجلد البناء
    build_dir = 'build_exe'
    
    # إنشاء مجلد البناء إذا لم يكن موجوداً
    if not os.path.exists(build_dir):
        os.makedirs(build_dir)
    
    # إعدادات PyInstaller
    options = [
        main_file,
        '--onefile',  # ملف واحد
        '--windowed',  # بدون نافذة وحدة التحكم
        '--name=محلات_أبو_علاء_CRM',  # اسم الملف التنفيذي
        f'--distpath={build_dir}',  # مجلد المخرجات
        '--add-data=templates;templates',  # إضافة مجلد القوالب
        '--add-data=static;static',  # إضافة مجلد الملفات الثابتة
        '--add-data=uploads;uploads',  # إضافة مجلد الرفع
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--hidden-import=openpyxl',
        '--hidden-import=reportlab',
        '--hidden-import=qrcode',
        '--hidden-import=PIL',
        '--hidden-import=bcrypt',
        '--hidden-import=jwt',
        '--clean',  # تنظيف الملفات المؤقتة
    ]
    
    print("جاري بناء ملف التنفيذي للويندوز...")
    print("هذا قد يستغرق بضع دقائق...")
    
    try:
        PyInstaller.__main__.run(options)
        print(f"\n✅ تم بناء الملف التنفيذي بنجاح!")
        print(f"📁 الملف موجود في: {build_dir}/محلات_أبو_علاء_CRM.exe")
        print("\n📋 ملاحظات مهمة:")
        print("- تأكد من وجود مجلد 'uploads' في نفس مجلد الملف التنفيذي")
        print("- سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل")
        print("- بيانات الدخول الافتراضية: admin / admin123")
        
    except Exception as e:
        print(f"❌ حدث خطأ أثناء البناء: {e}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe() 