const { app, BrowserWindow, <PERSON><PERSON>, <PERSON>ray, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');
const Store = require('electron-store');

// Initialize electron store for settings
const store = new Store();

class CRMElectronApp {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.flaskProcess = null;
        this.serverPort = 5000;
        this.serverUrl = `http://127.0.0.1:${this.serverPort}`;
        this.isQuitting = false;
    }

    async initialize() {
        // Set up app event listeners
        app.whenReady().then(() => this.createWindow());
        app.on('window-all-closed', () => this.handleWindowsClosed());
        app.on('activate', () => this.handleActivate());
        app.on('before-quit', () => this.handleBeforeQuit());

        // Handle second instance
        const gotTheLock = app.requestSingleInstanceLock();
        if (!gotTheLock) {
            app.quit();
        } else {
            app.on('second-instance', () => {
                if (this.mainWindow) {
                    if (this.mainWindow.isMinimized()) this.mainWindow.restore();
                    this.mainWindow.focus();
                }
            });
        }
    }

    async createWindow() {
        // Create the browser window
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            icon: path.join(__dirname, 'assets', 'icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            show: false, // Don't show until ready
            titleBarStyle: 'default'
        });

        // Set up window event listeners
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // Focus window on first launch
            if (process.platform === 'darwin') {
                app.dock.show();
            }
        });

        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting) {
                event.preventDefault();
                this.mainWindow.hide();
                
                // Show notification on first minimize
                if (!store.get('hasShownTrayNotification')) {
                    this.showTrayNotification();
                    store.set('hasShownTrayNotification', true);
                }
            }
        });

        // Create system tray
        this.createTray();

        // Create application menu
        this.createMenu();

        // Start Flask server and load app
        await this.startFlaskServer();
    }

    createTray() {
        const iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
        this.tray = new Tray(iconPath);
        
        const contextMenu = Menu.buildFromTemplate([
            {
                label: 'Show App',
                click: () => {
                    this.mainWindow.show();
                    if (process.platform === 'darwin') {
                        app.dock.show();
                    }
                }
            },
            {
                label: 'Open in Browser',
                click: () => shell.openExternal(this.serverUrl)
            },
            { type: 'separator' },
            {
                label: 'Restart Server',
                click: () => this.restartFlaskServer()
            },
            { type: 'separator' },
            {
                label: 'Quit',
                click: () => {
                    this.isQuitting = true;
                    app.quit();
                }
            }
        ]);

        this.tray.setToolTip('Abu Alaa Stores CRM');
        this.tray.setContextMenu(contextMenu);
        
        // Double click to show window
        this.tray.on('double-click', () => {
            this.mainWindow.show();
        });
    }

    createMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Customer',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.mainWindow.webContents.send('menu-action', 'new-customer')
                    },
                    { type: 'separator' },
                    {
                        label: 'Export Data',
                        click: () => this.mainWindow.webContents.send('menu-action', 'export-data')
                    },
                    { type: 'separator' },
                    {
                        label: 'Quit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => this.showAboutDialog()
                    },
                    {
                        label: 'Open Data Folder',
                        click: () => shell.openPath(path.dirname(app.getPath('userData')))
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    async startFlaskServer() {
        try {
            // Show loading screen
            this.mainWindow.loadFile(path.join(__dirname, 'loading.html'));

            // Find available port
            this.serverPort = await this.findAvailablePort(5000);
            this.serverUrl = `http://127.0.0.1:${this.serverPort}`;

            // Start Flask server
            const pythonExecutable = process.platform === 'win32' ? 'python.exe' : 'python3';
            const appPath = path.join(__dirname, '..', 'app.py');
            
            this.flaskProcess = spawn(pythonExecutable, [appPath, '--desktop'], {
                env: { ...process.env, FLASK_PORT: this.serverPort.toString() },
                cwd: path.dirname(appPath)
            });

            this.flaskProcess.stdout.on('data', (data) => {
                console.log(`Flask: ${data}`);
            });

            this.flaskProcess.stderr.on('data', (data) => {
                console.error(`Flask Error: ${data}`);
            });

            this.flaskProcess.on('close', (code) => {
                console.log(`Flask process exited with code ${code}`);
                if (!this.isQuitting) {
                    this.showServerErrorDialog();
                }
            });

            // Wait for server to start
            await this.waitForServer();
            
            // Load the application
            this.mainWindow.loadURL(this.serverUrl);

        } catch (error) {
            console.error('Failed to start Flask server:', error);
            this.showServerErrorDialog();
        }
    }

    async findAvailablePort(startPort) {
        // Simple port finding - in production, use a proper port finder
        for (let port = startPort; port < startPort + 100; port++) {
            try {
                await axios.get(`http://127.0.0.1:${port}`, { timeout: 100 });
            } catch (error) {
                if (error.code === 'ECONNREFUSED') {
                    return port; // Port is available
                }
            }
        }
        return startPort; // Fallback
    }

    async waitForServer(maxAttempts = 30) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await axios.get(this.serverUrl, { timeout: 1000 });
                return true;
            } catch (error) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        throw new Error('Server failed to start');
    }

    async restartFlaskServer() {
        if (this.flaskProcess) {
            this.flaskProcess.kill();
        }
        await this.startFlaskServer();
    }

    showTrayNotification() {
        this.tray.displayBalloon({
            iconType: 'info',
            title: 'Abu Alaa Stores CRM',
            content: 'Application minimized to tray. Double-click the tray icon to restore.'
        });
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Abu Alaa Stores CRM',
            message: 'Abu Alaa Stores CRM',
            detail: 'Desktop Customer Relationship Management System\nVersion 2.0.0\n\nBuilt with Electron and Flask'
        });
    }

    showServerErrorDialog() {
        dialog.showErrorBox(
            'Server Error',
            'The CRM server failed to start. Please check your Python installation and try again.'
        );
    }

    handleWindowsClosed() {
        if (process.platform !== 'darwin') {
            this.isQuitting = true;
            app.quit();
        }
    }

    handleActivate() {
        if (BrowserWindow.getAllWindows().length === 0) {
            this.createWindow();
        }
    }

    handleBeforeQuit() {
        this.isQuitting = true;
        if (this.flaskProcess) {
            this.flaskProcess.kill();
        }
    }
}

// Initialize and start the application
const crmApp = new CRMElectronApp();
crmApp.initialize();
