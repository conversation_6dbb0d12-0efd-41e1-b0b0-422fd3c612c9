{"name": "abu-alaa-stores-crm", "version": "2.0.0", "description": "Abu Alaa Stores CRM Desktop Application", "main": "electron/main.js", "homepage": "./", "scripts": {"electron": "electron .", "electron-dev": "ELECTRON_IS_DEV=true electron .", "build-electron": "electron-builder", "build-python": "python build_exe.py", "build-all": "npm run build-python && npm run build-electron", "start": "npm run electron", "dev": "npm run electron-dev", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.abualaastores.crm", "productName": "Abu Alaa Stores CRM", "directories": {"output": "dist_electron"}, "files": ["electron/**/*", "templates/**/*", "static/**/*", "uploads/**/*", "app.py", "requirements.txt", "*.exe", "!node_modules", "!src", "!build", "!dist", "!.git"], "extraResources": [{"from": "dist/AbuAlaaStoresCRM.exe", "to": "AbuAlaaStoresCRM.exe", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "electron/assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Abu Alaa Stores CRM"}, "mac": {"target": "dmg", "icon": "electron/assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "electron/assets/icon.png"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.5.0", "electron-store": "^8.1.0", "node-fetch": "^3.3.2"}, "author": {"name": "Abu Alaa Stores", "email": "<EMAIL>"}, "license": "UNLICENSED", "private": true}