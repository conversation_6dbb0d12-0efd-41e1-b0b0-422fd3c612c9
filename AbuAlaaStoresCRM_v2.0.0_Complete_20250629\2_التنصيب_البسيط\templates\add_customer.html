{% extends "base.html" %}

{% block title %}إضافة زبون جديد{% endblock %}

{% block content %}
<div class="form-page">
    <div class="page-header">
        <h1>إضافة زبون جديد</h1>
        <p>أدخل بيانات الزبون الجديد</p>
    </div>

    <div class="form-container">
        <form id="addCustomerForm" class="customer-form">
            <div class="form-section">
                <h3>البيانات الأساسية</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">اسم الزبون *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الهاتف *</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email">
                    </div>
                    
                    <div class="form-group">
                        <label for="national_id">الرقم الوطني</label>
                        <input type="text" id="national_id" name="national_id">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="address">العنوان</label>
                    <textarea id="address" name="address" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="company_name">اسم الشركة</label>
                    <input type="text" id="company_name" name="company_name">
                </div>
            </div>
            
            <div class="form-section">
                <h3>التصنيف والحالة</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="category">الفئة</label>
                        <select id="category" name="category">
                            <option value="عادي">عادي</option>
                            <option value="VIP">VIP</option>
                            <option value="مميز">مميز</option>
                            <option value="جديد">جديد</option>
                            <option value="قديم">قديم</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">الحالة</label>
                        <select id="status" name="status">
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                            <option value="محظور">محظور</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h3>معلومات إضافية</h3>
                
                <div class="form-group">
                    <label for="notes">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="4" placeholder="أي ملاحظات إضافية حول الزبون..."></textarea>
                </div>
            </div>
            
            <div class="form-actions">
                <a href="{{ url_for('customers') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ الزبون
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('name').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        national_id: document.getElementById('national_id').value,
        address: document.getElementById('address').value,
        company_name: document.getElementById('company_name').value,
        category: document.getElementById('category').value,
        status: document.getElementById('status').value,
        notes: document.getElementById('notes').value
    };
    
    fetch('/customers/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => {
                window.location.href = '{{ url_for("customers") }}';
            }, 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ في الاتصال', 'error');
    });
});
</script>
{% endblock %} 