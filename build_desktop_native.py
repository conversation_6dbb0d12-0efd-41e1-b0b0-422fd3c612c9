#!/usr/bin/env python3
"""
سكريبت بناء التطبيق المحلي - نظام إدارة الزبائن
يقوم ببناء تطبيق .NET WinForms محلي بالكامل
"""

import subprocess
import os
import sys
import shutil
import json
from datetime import datetime

class DesktopNativeBuilder:
    def __init__(self):
        self.build_log = []
        self.project_name = "CRMDesktopApp"
        self.output_dir = "publish_native"
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        print(log_entry)
    
    def check_dotnet(self):
        """فحص وجود .NET SDK"""
        try:
            result = subprocess.run(['dotnet', '--version'], 
                                  capture_output=True, text=True, check=True)
            version = result.stdout.strip()
            self.log(f"تم العثور على .NET SDK {version}")
            
            # فحص إصدار .NET
            major_version = int(version.split('.')[0])
            if major_version >= 6:
                self.log("إصدار .NET متوافق")
                return True
            else:
                self.log(f"يُنصح بـ .NET 6 أو أحدث (الموجود: {version})", "WARNING")
                return True
        except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
            self.log(".NET SDK غير موجود أو غير قابل للوصول", "ERROR")
            self.log("يرجى تثبيت .NET 6 SDK من https://dotnet.microsoft.com/download")
            return False
    
    def check_project_files(self):
        """فحص ملفات المشروع المطلوبة"""
        self.log("فحص ملفات المشروع...")
        
        required_files = [
            f"{self.project_name}.csproj",
            "Program.cs",
            "appsettings.json"
        ]
        
        required_dirs = [
            "Models",
            "Services", 
            "Data",
            "Forms"
        ]
        
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                missing_files.append(f"{dir_name}/")
        
        if missing_files:
            self.log(f"ملفات مفقودة: {', '.join(missing_files)}", "ERROR")
            return False
        
        self.log("جميع ملفات المشروع موجودة")
        return True
    
    def restore_packages(self):
        """استعادة حزم NuGet"""
        self.log("استعادة حزم NuGet...")
        try:
            result = subprocess.run(['dotnet', 'restore'], 
                                  capture_output=True, text=True, check=True)
            self.log("تم استعادة الحزم بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"فشل في استعادة الحزم: {e}", "ERROR")
            if e.stderr:
                self.log(f"تفاصيل الخطأ: {e.stderr}", "ERROR")
            return False
    
    def build_debug(self):
        """بناء إصدار التطوير"""
        self.log("بناء إصدار التطوير...")
        try:
            result = subprocess.run(['dotnet', 'build', '--configuration', 'Debug'], 
                                  capture_output=True, text=True, check=True)
            self.log("تم بناء إصدار التطوير بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"فشل في بناء إصدار التطوير: {e}", "ERROR")
            if e.stderr:
                self.log(f"تفاصيل الخطأ: {e.stderr}", "ERROR")
            return False
    
    def build_release(self):
        """بناء إصدار الإنتاج"""
        self.log("بناء إصدار الإنتاج...")
        try:
            result = subprocess.run(['dotnet', 'build', '--configuration', 'Release'], 
                                  capture_output=True, text=True, check=True)
            self.log("تم بناء إصدار الإنتاج بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"فشل في بناء إصدار الإنتاج: {e}", "ERROR")
            if e.stderr:
                self.log(f"تفاصيل الخطأ: {e.stderr}", "ERROR")
            return False
    
    def publish_self_contained(self):
        """نشر تطبيق مستقل"""
        self.log("نشر التطبيق المستقل...")
        
        # حذف مجلد الإخراج إذا كان موجوداً
        if os.path.exists(self.output_dir):
            shutil.rmtree(self.output_dir)
        
        try:
            cmd = [
                'dotnet', 'publish',
                '--configuration', 'Release',
                '--runtime', 'win-x64',
                '--self-contained', 'true',
                '--output', self.output_dir,
                '/p:PublishSingleFile=true',
                '/p:PublishReadyToRun=true',
                '/p:IncludeNativeLibrariesForSelfExtract=true',
                '/p:EnableCompressionInSingleFile=true'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.log("تم نشر التطبيق المستقل بنجاح")
            
            # فحص الملف المنشور
            exe_path = os.path.join(self.output_dir, f"{self.project_name}.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # بالميجابايت
                self.log(f"الملف التنفيذي: {exe_path}")
                self.log(f"حجم الملف: {file_size:.1f} ميجابايت")
                return True
            else:
                self.log("لم يتم العثور على الملف التنفيذي", "ERROR")
                return False
                
        except subprocess.CalledProcessError as e:
            self.log(f"فشل في نشر التطبيق: {e}", "ERROR")
            if e.stderr:
                self.log(f"تفاصيل الخطأ: {e.stderr}", "ERROR")
            return False
    
    def publish_framework_dependent(self):
        """نشر تطبيق يعتمد على إطار العمل"""
        self.log("نشر التطبيق المعتمد على إطار العمل...")
        
        output_dir = "publish_framework"
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        
        try:
            cmd = [
                'dotnet', 'publish',
                '--configuration', 'Release',
                '--output', output_dir
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.log("تم نشر التطبيق المعتمد على إطار العمل بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"فشل في نشر التطبيق المعتمد على إطار العمل: {e}", "ERROR")
            return False
    
    def create_launcher_script(self):
        """إنشاء سكريبت تشغيل"""
        self.log("إنشاء سكريبت التشغيل...")
        
        launcher_script = f'''@echo off
chcp 65001 >nul
title نظام إدارة الزبائن - محلات أبو علاء
color 0A

echo.
echo ========================================
echo    نظام إدارة الزبائن - محلات أبو علاء
echo           تطبيق سطح المكتب المحلي
echo ========================================
echo.
echo 🚀 جاري بدء تشغيل النظام...
echo.
echo 📋 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo ⚠️  يُرجى تغيير كلمة المرور فور الدخول الأول
echo.
echo 💾 قاعدة البيانات: محلية (SQLite)
echo 📁 مجلد البيانات: %%APPDATA%%\\محلات أبو علاء CRM
echo.

REM تشغيل التطبيق
"{self.project_name}.exe"

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo يرجى التأكد من:
    echo   • وجود جميع الملفات المطلوبة
    echo   • صلاحيات تشغيل التطبيق
    echo   • عدم حجب مكافح الفيروسات للتطبيق
    echo.
    pause
)
'''
        
        launcher_path = os.path.join(self.output_dir, "تشغيل_النظام.bat")
        with open(launcher_path, "w", encoding="utf-8") as f:
            f.write(launcher_script)
        
        self.log(f"تم إنشاء سكريبت التشغيل: {launcher_path}")
    
    def create_readme(self):
        """إنشاء ملف التعليمات"""
        self.log("إنشاء ملف التعليمات...")
        
        readme_content = f'''نظام إدارة الزبائن - محلات أبو علاء
===================================

🏪 تطبيق سطح مكتب محلي متطور لإدارة الزبائن والمبيعات

الإصدار: 3.0.0
تاريخ البناء: {datetime.now().strftime("%Y-%m-%d %H:%M")}

المميزات الرئيسية:
==================
✅ تطبيق سطح مكتب محلي (لا يحتاج إنترنت)
✅ قاعدة بيانات SQLite محلية وآمنة
✅ واجهة عربية كاملة
✅ إدارة شاملة للزبائن
✅ تتبع المبيعات والمدفوعات
✅ تقارير مالية مفصلة
✅ نظام تذكيرات متقدم
✅ إدارة المستندات
✅ نسخ احتياطية تلقائية

طريقة التشغيل:
===============
1️⃣ انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
2️⃣ أو شغل "{self.project_name}.exe" مباشرة
3️⃣ استخدم بيانات الدخول الافتراضية:
   👤 اسم المستخدم: admin
   🔑 كلمة المرور: admin123

⚠️ مهم جداً: غيّر كلمة المرور فوراً بعد الدخول الأول!

متطلبات النظام:
================
• نظام التشغيل: Windows 7 أو أحدث (64-bit)
• الذاكرة: 2 جيجابايت RAM كحد أدنى
• مساحة القرص: 200 ميجابايت
• لا يحتاج اتصال بالإنترنت

مجلدات البيانات:
=================
📁 قاعدة البيانات: %APPDATA%\\محلات أبو علاء CRM\\CRM_Database.db
📁 المستندات: %APPDATA%\\محلات أبو علاء CRM\\Documents\\
📁 النسخ الاحتياطية: %APPDATA%\\محلات أبو علاء CRM\\Backups\\
📁 التقارير: %APPDATA%\\محلات أبو علاء CRM\\Reports\\

استكشاف الأخطاء:
==================
❓ إذا لم يعمل التطبيق:
   • تأكد من تشغيله كمدير
   • تحقق من إعدادات مكافح الفيروسات
   • أعد تشغيل الكمبيوتر

❓ مشاكل في قاعدة البيانات:
   • احذف ملف CRM_Database.db وأعد تشغيل التطبيق
   • سيتم إنشاء قاعدة بيانات جديدة تلقائياً

❓ للمساعدة الإضافية:
   • راجع ملف السجل في مجلد التطبيق
   • تواصل مع الدعم الفني

النسخ الاحتياطية:
==================
💾 يُنصح بعمل نسخة احتياطية دورية من:
   • ملف قاعدة البيانات
   • مجلد المستندات
   • إعدادات التطبيق

🔄 النسخ الاحتياطي التلقائي:
   • يتم تلقائياً كل يوم في الساعة 2:00 صباحاً
   • يحتفظ بآخر 30 نسخة احتياطية
   • يمكن تعطيله من إعدادات التطبيق

الدعم الفني:
============
📧 للدعم الفني والاستفسارات
📞 متاح خلال ساعات العمل الرسمية
🔧 تحديثات مجانية

© 2024 محلات أبو علاء - جميع الحقوق محفوظة
تطبيق سطح مكتب محلي - لا يحتاج اتصال بالإنترنت
'''
        
        readme_path = os.path.join(self.output_dir, "اقرأني.txt")
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        self.log(f"تم إنشاء ملف التعليمات: {readme_path}")
    
    def copy_additional_files(self):
        """نسخ الملفات الإضافية"""
        self.log("نسخ الملفات الإضافية...")
        
        files_to_copy = [
            "appsettings.json"
        ]
        
        for file in files_to_copy:
            if os.path.exists(file):
                dest_path = os.path.join(self.output_dir, file)
                shutil.copy2(file, dest_path)
                self.log(f"تم نسخ: {file}")
    
    def save_build_log(self):
        """حفظ سجل البناء"""
        log_content = "\n".join(self.build_log)
        log_path = os.path.join(self.output_dir, "build_log.txt")
        with open(log_path, "w", encoding="utf-8") as f:
            f.write(log_content)
        self.log(f"تم حفظ سجل البناء: {log_path}")
    
    def print_summary(self):
        """طباعة ملخص البناء"""
        print("\n" + "=" * 60)
        print("ملخص البناء")
        print("=" * 60)
        
        if os.path.exists(self.output_dir):
            exe_path = os.path.join(self.output_dir, f"{self.project_name}.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"✅ تم بناء التطبيق بنجاح!")
                print(f"📁 مجلد الإخراج: {os.path.abspath(self.output_dir)}")
                print(f"📄 الملف التنفيذي: {self.project_name}.exe ({file_size:.1f} MB)")
                print(f"🚀 ملف التشغيل: تشغيل_النظام.bat")
                print(f"📖 ملف التعليمات: اقرأني.txt")
                
                print(f"\n📋 الخطوات التالية:")
                print(f"1. اختبر التطبيق على النظام الحالي")
                print(f"2. انسخ مجلد '{self.output_dir}' إلى الأنظمة المستهدفة")
                print(f"3. شغل 'تشغيل_النظام.bat' لبدء التطبيق")
                print(f"4. غيّر كلمة المرور الافتراضية")
                
                return True
            else:
                print("❌ فشل في إنشاء الملف التنفيذي")
                return False
        else:
            print("❌ فشل في إنشاء مجلد الإخراج")
            return False

def main():
    """الدالة الرئيسية"""
    builder = DesktopNativeBuilder()
    
    print("🚀 بناء التطبيق المحلي - نظام إدارة الزبائن")
    print("=" * 60)
    
    # فحص المتطلبات
    if not builder.check_dotnet():
        input("اضغط Enter للخروج...")
        return False
    
    if not builder.check_project_files():
        input("اضغط Enter للخروج...")
        return False
    
    # استعادة الحزم
    if not builder.restore_packages():
        input("اضغط Enter للخروج...")
        return False
    
    # بناء التطبيق
    print("\n🤔 ما نوع البناء المطلوب؟")
    print("1. بناء سريع (Debug)")
    print("2. بناء الإنتاج (Release)")
    print("3. تطبيق مستقل (موصى به)")
    print("4. تطبيق معتمد على إطار العمل")
    
    choice = input("اختر (1-4): ").strip()
    
    success = False
    
    if choice == '1':
        success = builder.build_debug()
    elif choice == '2':
        success = builder.build_release()
    elif choice == '3':
        success = (builder.build_release() and 
                  builder.publish_self_contained())
        if success:
            builder.create_launcher_script()
            builder.create_readme()
            builder.copy_additional_files()
            builder.save_build_log()
    elif choice == '4':
        success = (builder.build_release() and 
                  builder.publish_framework_dependent())
    else:
        print("❌ اختيار غير صحيح")
        input("اضغط Enter للخروج...")
        return False
    
    # طباعة الملخص
    if choice == '3':
        builder.print_summary()
    elif success:
        print("\n✅ تم البناء بنجاح!")
        print(f"📁 تحقق من مجلد: bin\\Release\\net6.0-windows\\")
    else:
        print("\n❌ فشل في البناء!")
    
    input("\nاضغط Enter للخروج...")
    return success

if __name__ == "__main__":
    main()
