<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading - Abu Alaa Stores CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .loading-container {
            text-align: center;
            max-width: 400px;
            padding: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.7;
            margin-bottom: 40px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            margin: 0 auto 20px;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            opacity: 0.8;
            animation: fade 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 30px 0 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: progress 3s infinite;
        }

        .status-messages {
            margin-top: 20px;
            font-size: 12px;
            opacity: 0.6;
            line-height: 1.5;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes fade {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        @keyframes progress {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .features {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            opacity: 0.6;
            font-size: 12px;
        }

        .features ul {
            list-style: none;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .features li {
            padding: 5px 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">CRM</div>
        <h1 class="title">Abu Alaa Stores CRM</h1>
        <p class="subtitle">Customer Relationship Management System</p>
        
        <div class="loading-spinner"></div>
        <div class="loading-text" id="loadingText">Starting application...</div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="status-messages" id="statusMessages">
            <div>• Initializing database...</div>
            <div>• Starting server...</div>
            <div>• Loading interface...</div>
        </div>
    </div>

    <div class="features">
        <ul>
            <li>Customer Management</li>
            <li>Transaction Tracking</li>
            <li>Financial Reports</li>
            <li>Document Storage</li>
        </ul>
    </div>

    <script>
        // Simulate loading progress
        const loadingTexts = [
            'Starting application...',
            'Initializing database...',
            'Starting server...',
            'Loading interface...',
            'Almost ready...'
        ];

        let currentTextIndex = 0;
        const loadingTextElement = document.getElementById('loadingText');

        function updateLoadingText() {
            loadingTextElement.textContent = loadingTexts[currentTextIndex];
            currentTextIndex = (currentTextIndex + 1) % loadingTexts.length;
        }

        // Update loading text every 2 seconds
        setInterval(updateLoadingText, 2000);

        // Add some interactivity
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                // Allow closing during loading if needed
                window.close();
            }
        });

        // Prevent context menu
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
    </script>
</body>
</html>
