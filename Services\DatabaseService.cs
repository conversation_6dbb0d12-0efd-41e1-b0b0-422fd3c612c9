using Microsoft.EntityFrameworkCore;
using CRMDesktopApp.Data;
using CRMDesktopApp.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CRMDesktopApp.Services
{
    public interface IDatabaseService
    {
        Task<bool> InitializeDatabaseAsync();
        Task<bool> TestConnectionAsync();
        Task<User?> AuthenticateUserAsync(string username, string password);
        Task<bool> CreateUserAsync(User user, string password);
        Task<List<Customer>> GetCustomersAsync(int userId);
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<bool> SaveCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<List<Transaction>> GetTransactionsAsync(int customerId);
        Task<bool> SaveTransactionAsync(Transaction transaction);
        Task<List<Payment>> GetPaymentsAsync(int customerId);
        Task<bool> SavePaymentAsync(Payment payment);
        Task<List<Communication>> GetCommunicationsAsync(int customerId);
        Task<bool> SaveCommunicationAsync(Communication communication);
        Task<List<Document>> GetDocumentsAsync(int customerId);
        Task<bool> SaveDocumentAsync(Document document);
        Task<List<Reminder>> GetRemindersAsync(int userId);
        Task<bool> SaveReminderAsync(Reminder reminder);
        Task<Dictionary<string, object>> GetDashboardStatsAsync(int userId);
    }

    public class DatabaseService : IDatabaseService
    {
        private readonly CRMDbContext _context;
        private readonly ILogger<DatabaseService> _logger;

        public DatabaseService(CRMDbContext context, ILogger<DatabaseService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("Initializing database...");

                // Test connection first
                var canConnect = await _context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    _logger.LogInformation("Database doesn't exist, creating...");
                }

                // Ensure database is created
                var created = await _context.Database.EnsureCreatedAsync();
                if (created)
                {
                    _logger.LogInformation("Database created successfully");
                }
                else
                {
                    _logger.LogInformation("Database already exists");
                }

                // Verify connection again
                canConnect = await _context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    _logger.LogError("Failed to connect to database after creation");
                    return false;
                }

                // Check if admin user exists, if not create one
                var adminExists = await _context.Users.AnyAsync(u => u.Username == "admin");
                if (!adminExists)
                {
                    _logger.LogInformation("Creating admin user...");
                    var adminUser = new User
                    {
                        Username = "admin",
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        Role = "مدير",
                        FullName = "مدير النظام",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    _context.Users.Add(adminUser);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Admin user created successfully");
                }
                else
                {
                    _logger.LogInformation("Admin user already exists");
                }

                _logger.LogInformation("Database initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing database: {Message}", ex.Message);

                // Try to provide more specific error information
                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "Inner exception: {Message}", ex.InnerException.Message);
                }

                return false;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                return await _context.Database.CanConnectAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed");
                return false;
            }
        }

        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user != null && BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    // Update last login
                    user.LastLogin = DateTime.Now;
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation($"User {username} authenticated successfully");
                    return user;
                }

                _logger.LogWarning($"Authentication failed for user {username}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error authenticating user {username}");
                return null;
            }
        }

        public async Task<bool> CreateUserAsync(User user, string password)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == user.Username);

                if (existingUser != null)
                {
                    _logger.LogWarning($"Username {user.Username} already exists");
                    return false;
                }

                // Hash password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
                user.CreatedAt = DateTime.Now;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"User {user.Username} created successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating user {user.Username}");
                return false;
            }
        }

        public async Task<List<Customer>> GetCustomersAsync(int userId)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.UserId == userId)
                    .Include(c => c.Transactions)
                    .Include(c => c.Payments)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                return new List<Customer>();
            }
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            try
            {
                return await _context.Customers
                    .Include(c => c.Transactions)
                    .Include(c => c.Payments)
                    .Include(c => c.Communications)
                    .Include(c => c.Documents)
                    .Include(c => c.Reminders)
                    .FirstOrDefaultAsync(c => c.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting customer {id}");
                return null;
            }
        }

        public async Task<bool> SaveCustomerAsync(Customer customer)
        {
            try
            {
                if (customer.Id == 0)
                {
                    // New customer
                    customer.CreatedAt = DateTime.Now;
                    customer.UpdatedAt = DateTime.Now;
                    _context.Customers.Add(customer);
                }
                else
                {
                    // Update existing customer
                    customer.UpdatedAt = DateTime.Now;
                    _context.Customers.Update(customer);
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Customer {customer.Name} saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error saving customer {customer.Name}");
                return false;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer != null)
                {
                    _context.Customers.Remove(customer);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Customer {id} deleted successfully");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting customer {id}");
                return false;
            }
        }

        public async Task<List<Transaction>> GetTransactionsAsync(int customerId)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.CustomerId == customerId)
                    .Include(t => t.Customer)
                    .Include(t => t.User)
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting transactions for customer {customerId}");
                return new List<Transaction>();
            }
        }

        public async Task<bool> SaveTransactionAsync(Transaction transaction)
        {
            try
            {
                if (transaction.Id == 0)
                {
                    transaction.CreatedAt = DateTime.Now;
                    _context.Transactions.Add(transaction);
                }
                else
                {
                    _context.Transactions.Update(transaction);
                }

                await _context.SaveChangesAsync();

                // Update customer balance
                await UpdateCustomerBalanceAsync(transaction.CustomerId);

                _logger.LogInformation($"Transaction saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving transaction");
                return false;
            }
        }

        public async Task<List<Payment>> GetPaymentsAsync(int customerId)
        {
            try
            {
                return await _context.Payments
                    .Where(p => p.CustomerId == customerId)
                    .Include(p => p.Customer)
                    .Include(p => p.User)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting payments for customer {customerId}");
                return new List<Payment>();
            }
        }

        public async Task<bool> SavePaymentAsync(Payment payment)
        {
            try
            {
                if (payment.Id == 0)
                {
                    payment.CreatedAt = DateTime.Now;
                    _context.Payments.Add(payment);
                }
                else
                {
                    _context.Payments.Update(payment);
                }

                await _context.SaveChangesAsync();

                // Update customer balance
                await UpdateCustomerBalanceAsync(payment.CustomerId);

                _logger.LogInformation($"Payment saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving payment");
                return false;
            }
        }

        private async Task UpdateCustomerBalanceAsync(int customerId)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer != null)
                {
                    var totalSales = await _context.Transactions
                        .Where(t => t.CustomerId == customerId && t.TransactionType == "بيع")
                        .SumAsync(t => t.Amount);

                    var totalPayments = await _context.Payments
                        .Where(p => p.CustomerId == customerId)
                        .SumAsync(p => p.Amount);

                    customer.TotalBalance = totalSales;
                    customer.PaidAmount = totalPayments;
                    customer.PendingAmount = totalSales - totalPayments;
                    customer.UpdatedAt = DateTime.Now;

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating customer balance for customer {customerId}");
            }
        }

        public async Task<List<Communication>> GetCommunicationsAsync(int customerId)
        {
            try
            {
                return await _context.Communications
                    .Where(c => c.CustomerId == customerId)
                    .Include(c => c.User)
                    .OrderByDescending(c => c.Timestamp)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting communications for customer {customerId}");
                return new List<Communication>();
            }
        }

        public async Task<bool> SaveCommunicationAsync(Communication communication)
        {
            try
            {
                if (communication.Id == 0)
                {
                    _context.Communications.Add(communication);
                }
                else
                {
                    _context.Communications.Update(communication);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving communication");
                return false;
            }
        }

        public async Task<List<Document>> GetDocumentsAsync(int customerId)
        {
            try
            {
                return await _context.Documents
                    .Where(d => d.CustomerId == customerId)
                    .Include(d => d.User)
                    .OrderByDescending(d => d.UploadDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting documents for customer {customerId}");
                return new List<Document>();
            }
        }

        public async Task<bool> SaveDocumentAsync(Document document)
        {
            try
            {
                if (document.Id == 0)
                {
                    _context.Documents.Add(document);
                }
                else
                {
                    _context.Documents.Update(document);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving document");
                return false;
            }
        }

        public async Task<List<Reminder>> GetRemindersAsync(int userId)
        {
            try
            {
                return await _context.Reminders
                    .Where(r => r.UserId == userId)
                    .Include(r => r.Customer)
                    .OrderBy(r => r.DueDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting reminders for user {userId}");
                return new List<Reminder>();
            }
        }

        public async Task<bool> SaveReminderAsync(Reminder reminder)
        {
            try
            {
                if (reminder.Id == 0)
                {
                    reminder.CreatedAt = DateTime.Now;
                    _context.Reminders.Add(reminder);
                }
                else
                {
                    _context.Reminders.Update(reminder);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving reminder");
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetDashboardStatsAsync(int userId)
        {
            try
            {
                var stats = new Dictionary<string, object>();

                // Customer statistics
                var totalCustomers = await _context.Customers
                    .Where(c => c.UserId == userId)
                    .CountAsync();

                var activeCustomers = await _context.Customers
                    .Where(c => c.UserId == userId && c.Status == "نشط")
                    .CountAsync();

                // Financial statistics
                var totalSales = await _context.Transactions
                    .Where(t => t.User!.Id == userId && t.TransactionType == "بيع")
                    .SumAsync(t => t.Amount);

                var totalPayments = await _context.Payments
                    .Where(p => p.User!.Id == userId)
                    .SumAsync(p => p.Amount);

                var pendingAmount = await _context.Customers
                    .Where(c => c.UserId == userId)
                    .SumAsync(c => c.PendingAmount);

                // Recent activity
                var recentTransactions = await _context.Transactions
                    .Where(t => t.User!.Id == userId)
                    .OrderByDescending(t => t.TransactionDate)
                    .Take(5)
                    .CountAsync();

                var overdueReminders = await _context.Reminders
                    .Where(r => r.UserId == userId && r.DueDate < DateTime.Now && r.Status == "معلق")
                    .CountAsync();

                stats["TotalCustomers"] = totalCustomers;
                stats["ActiveCustomers"] = activeCustomers;
                stats["TotalSales"] = totalSales;
                stats["TotalPayments"] = totalPayments;
                stats["PendingAmount"] = pendingAmount;
                stats["RecentTransactions"] = recentTransactions;
                stats["OverdueReminders"] = overdueReminders;

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting dashboard stats for user {userId}");
                return new Dictionary<string, object>();
            }
        }
    }
}
