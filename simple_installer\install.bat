@echo off
chcp 65001 >nul
title تثبيت نظام إدارة الزبائن - محلات أبو علاء
color 0A

echo.
echo ========================================
echo    تثبيت نظام إدارة الزبائن
echo        محلات أبو علاء
echo ========================================
echo.
echo الإصدار: 2.0.0
echo التاريخ: 2025-06-29
echo.

echo جاري التحقق من المتطلبات...
timeout /t 2 >nul

REM التحقق من نظام التشغيل
ver | find "Windows" >nul
if errorlevel 1 (
    echo ❌ هذا البرنامج يعمل على Windows فقط
    pause
    exit /b 1
)

echo ✅ نظام التشغيل متوافق

REM إنشاء مجلد التثبيت
set "INSTALL_DIR=%ProgramFiles%\Abu Alaa Stores CRM"
echo.
echo مجلد التثبيت: %INSTALL_DIR%
echo.

REM طلب صلاحيات المدير
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  يتطلب صلاحيات المدير للتثبيت
    echo يرجى تشغيل الملف كمدير
    pause
    exit /b 1
)

echo ✅ صلاحيات المدير متوفرة

echo.
echo جاري إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo جاري نسخ الملفات...
copy "AbuAlaaStoresCRM.exe" "%INSTALL_DIR%\" >nul
copy "README.md" "%INSTALL_DIR%\" >nul
copy "SETUP_GUIDE.md" "%INSTALL_DIR%\" >nul
copy "LICENSE.txt" "%INSTALL_DIR%\" >nul

echo جاري نسخ المجلدات...
xcopy "templates" "%INSTALL_DIR%\templates\" /E /I /Q >nul
xcopy "static" "%INSTALL_DIR%\static\" /E /I /Q >nul
xcopy "uploads" "%INSTALL_DIR%\uploads\" /E /I /Q >nul

echo جاري إنشاء الاختصارات...

REM اختصار سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"
echo [InternetShortcut] > "%DESKTOP%\محلات أبو علاء CRM.url"
echo URL=file:///%INSTALL_DIR%\AbuAlaaStoresCRM.exe >> "%DESKTOP%\محلات أبو علاء CRM.url"

REM اختصار قائمة ابدأ
set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%STARTMENU%\محلات أبو علاء" mkdir "%STARTMENU%\محلات أبو علاء"
echo [InternetShortcut] > "%STARTMENU%\محلات أبو علاء\محلات أبو علاء CRM.url"
echo URL=file:///%INSTALL_DIR%\AbuAlaaStoresCRM.exe >> "%STARTMENU%\محلات أبو علاء\محلات أبو علاء CRM.url"

REM إنشاء ملف إلغاء التثبيت
echo @echo off > "%INSTALL_DIR%\uninstall.bat"
echo title إلغاء تثبيت نظام إدارة الزبائن >> "%INSTALL_DIR%\uninstall.bat"
echo echo جاري إلغاء التثبيت... >> "%INSTALL_DIR%\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\uninstall.bat"
echo del "%DESKTOP%\محلات أبو علاء CRM.url" >> "%INSTALL_DIR%\uninstall.bat"
echo rd /s /q "%STARTMENU%\محلات أبو علاء" >> "%INSTALL_DIR%\uninstall.bat"
echo echo تم إلغاء التثبيت بنجاح >> "%INSTALL_DIR%\uninstall.bat"
echo pause >> "%INSTALL_DIR%\uninstall.bat"

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة ابدأ: تم إضافته
echo.
echo بيانات الدخول الافتراضية:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⚠️  يُرجى تغيير كلمة المرور فور الدخول الأول
echo.

set /p "START=هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%START%"=="y" (
    echo جاري تشغيل البرنامج...
    start "" "%INSTALL_DIR%\AbuAlaaStoresCRM.exe"
)

echo.
echo شكراً لاختيارك نظام إدارة الزبائن من محلات أبو علاء!
echo.
pause
