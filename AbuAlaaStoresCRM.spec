# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('static', 'static'), ('uploads', 'uploads')],
    hiddenimports=['flask', 'flask_sqlalchemy', 'flask_login', 'werkzeug', 'werkzeug.security', 'jinja2', 'openpyxl', 'openpyxl.styles', 'reportlab', 'reportlab.lib', 'reportlab.platypus', 'qrcode', 'PIL', 'PIL.Image', 'bcrypt', 'jwt', 'sqlite3', 'threading', 'webbrowser', 'socket', 'logging', 'datetime', 'uuid', 'json', 'io', 'signal', 'atexit'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=2,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [('O', None, 'OPTION'), ('O', None, 'OPTION')],
    name='AbuAlaaStoresCRM',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
