// Iraqi CRM - Main JavaScript File

class IraqiCRM {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAjaxDefaults();
        this.initializeComponents();
    }

    setupEventListeners() {
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }

        // Filter changes
        const filterSelects = document.querySelectorAll('.filter-select');
        filterSelects.forEach(select => {
            select.addEventListener('change', this.handleFilterChange.bind(this));
        });

        // Delete confirmations
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-btn')) {
                e.preventDefault();
                this.showDeleteConfirmation(e.target);
            }
        });

        // Modal confirmations
        const confirmButton = document.getElementById('confirmButton');
        if (confirmButton) {
            confirmButton.addEventListener('click', this.handleConfirmAction.bind(this));
        }

        // Auto-save forms
        const autoSaveForms = document.querySelectorAll('.auto-save');
        autoSaveForms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('change', this.debounce(this.autoSaveForm.bind(this), 1000));
            });
        });
    }

    setupAjaxDefaults() {
        // Set up CSRF token for AJAX requests
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (token) {
            $.ajaxSetup({
                headers: {
                    'X-CSRFToken': token
                }
            });
        }
    }

    initializeComponents() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.initializeCharts();
        }
    }

    // Form handling
    handleFormSubmit(e) {
        const form = e.target;
        const isAjaxForm = form.classList.contains('ajax-form');
        
        if (isAjaxForm) {
            e.preventDefault();
            this.submitFormAjax(form);
        }
    }

    async submitFormAjax(form) {
        const formData = new FormData(form);
        const url = form.action;
        const method = form.method || 'POST';

        try {
            this.showLoading();
            
            const response = await fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || 'Operation completed successfully', 'success');
                
                // Redirect if specified
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
                
                // Close modal if form is in modal
                const modal = form.closest('.modal');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
                
                // Refresh page if needed
                if (result.refresh) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                this.showNotification(result.message || 'An error occurred', 'error');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred while processing your request', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Search functionality
    handleSearch(e) {
        const searchTerm = e.target.value;
        const currentUrl = new URL(window.location);
        
        if (searchTerm) {
            currentUrl.searchParams.set('search', searchTerm);
        } else {
            currentUrl.searchParams.delete('search');
        }
        
        // Reset to first page when searching
        currentUrl.searchParams.delete('page');
        
        window.location.href = currentUrl.toString();
    }

    // Filter functionality
    handleFilterChange(e) {
        const filterName = e.target.name;
        const filterValue = e.target.value;
        const currentUrl = new URL(window.location);
        
        if (filterValue) {
            currentUrl.searchParams.set(filterName, filterValue);
        } else {
            currentUrl.searchParams.delete(filterName);
        }
        
        // Reset to first page when filtering
        currentUrl.searchParams.delete('page');
        
        window.location.href = currentUrl.toString();
    }

    // Delete confirmation
    showDeleteConfirmation(button) {
        const message = button.dataset.message || 'Are you sure you want to delete this item?';
        const action = button.dataset.action || button.href;
        
        const modal = document.getElementById('confirmModal');
        const messageEl = document.getElementById('confirmMessage');
        const confirmBtn = document.getElementById('confirmButton');
        
        messageEl.textContent = message;
        confirmBtn.dataset.action = action;
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    handleConfirmAction() {
        const action = this.confirmButton.dataset.action;
        
        if (action) {
            this.performDelete(action);
        }
    }

    async performDelete(url) {
        try {
            this.showLoading();
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || 'Item deleted successfully', 'success');
                
                // Close modal
                const modal = document.getElementById('confirmModal');
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
                
                // Refresh page or remove element
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                this.showNotification(result.message || 'Failed to delete item', 'error');
            }
        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('An error occurred while deleting', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Auto-save functionality
    autoSaveForm(e) {
        const form = e.target.closest('form');
        const formData = new FormData(form);
        
        // Add auto-save indicator
        this.showAutoSaveIndicator();
        
        // Send auto-save request
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-Auto-Save': 'true'
            }
        }).then(response => response.json())
        .then(result => {
            if (result.success) {
                this.hideAutoSaveIndicator();
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
            this.hideAutoSaveIndicator();
        });
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showLoading() {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.style.display = 'flex';
        }
    }

    hideLoading() {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="alert-close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        // Add to page
        const container = document.querySelector('.main-content') || document.body;
        container.insertBefore(notification, container.firstChild);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showAutoSaveIndicator() {
        let indicator = document.getElementById('auto-save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'auto-save-indicator';
            indicator.className = 'auto-save-indicator';
            indicator.innerHTML = '<i class="fas fa-save"></i> Saving...';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'block';
    }

    hideAutoSaveIndicator() {
        const indicator = document.getElementById('auto-save-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    // Chart initialization
    initializeCharts() {
        // Dashboard charts
        this.initializeDashboardCharts();
        
        // Customer analytics charts
        this.initializeCustomerCharts();
    }

    initializeDashboardCharts() {
        const ctx = document.getElementById('paymentsChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Payments Received',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: '#404040'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: '#404040'
                            }
                        }
                    }
                }
            });
        }
    }

    initializeCustomerCharts() {
        const ctx = document.getElementById('customerStatusChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Inactive', 'Blacklisted'],
                    datasets: [{
                        data: [65, 25, 10],
                        backgroundColor: [
                            '#28a745',
                            '#6c757d',
                            '#dc3545'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    }
                }
            });
        }
    }

    // Export functionality
    exportToExcel(tableId, filename) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const wb = XLSX.utils.table_to_book(table, { sheet: "Sheet1" });
        XLSX.writeFile(wb, filename || 'export.xlsx');
    }

    exportToPDF(elementId, filename) {
        const element = document.getElementById(elementId);
        if (!element) return;

        html2pdf().from(element).save(filename || 'export.pdf');
    }

    // QR Code generation
    generateQRCode(text, elementId) {
        const qr = qrcode(0, 'M');
        qr.addData(text);
        qr.make();
        
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = qr.createImgTag(5);
        }
    }

    // Currency formatting
    formatCurrency(amount, currency = 'IQD') {
        const formatter = new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
        return formatter.format(amount);
    }

    // Date formatting
    formatDate(date, format = 'short') {
        const dateObj = new Date(date);
        const options = {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' },
            time: { hour: '2-digit', minute: '2-digit' }
        };
        
        return dateObj.toLocaleDateString('ar-IQ', options[format]);
    }

    // Phone number formatting
    formatPhoneNumber(phone) {
        // Iraqi phone number formatting
        const cleaned = phone.replace(/\D/g, '');
        const match = cleaned.match(/^(\d{4})(\d{3})(\d{4})$/);
        if (match) {
            return `${match[1]} ${match[2]} ${match[3]}`;
        }
        return phone;
    }

    // Validation functions
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    validatePhone(phone) {
        const cleaned = phone.replace(/\D/g, '');
        return cleaned.length >= 10;
    }

    validateNationalID(id) {
        // Iraqi National ID validation (11 digits)
        const cleaned = id.replace(/\D/g, '');
        return cleaned.length === 11;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.iraqiCRM = new IraqiCRM();
});

// Service Worker for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Add CSS for auto-save indicator
const style = document.createElement('style');
style.textContent = `
    .auto-save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        z-index: 9999;
        display: none;
    }
    
    .notification-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
    }
`;
document.head.appendChild(style); 