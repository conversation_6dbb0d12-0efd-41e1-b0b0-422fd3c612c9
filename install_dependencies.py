#!/usr/bin/env python3
"""
Dependency installer for Abu Alaa Stores CRM Desktop Application
Installs all required packages for building the desktop application
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip"""
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_core_dependencies():
    """Install core dependencies for the Flask application"""
    core_packages = [
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5",
        "Flask-Login==0.6.3",
        "Flask-WTF==1.1.1",
        "Werkzeug==2.3.7",
        "openpyxl==3.1.2",
        "reportlab==4.0.4",
        "Pillow>=10.0.0",
        "bcrypt==4.0.1",
        "PyJWT==2.8.0",
        "cryptography>=41.0.0",
        "qrcode>=7.4.0",
        "requests==2.31.0",
        "python-dateutil==2.8.2",
        "python-dotenv==1.0.0"
    ]
    
    print("📦 Installing core Flask dependencies...")
    failed_packages = []
    
    for package in core_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    return len(failed_packages) == 0, failed_packages

def install_desktop_dependencies():
    """Install desktop-specific dependencies"""
    desktop_packages = [
        "PyInstaller==6.1.0",
        "pystray",
        "Pillow"  # Required for pystray
    ]
    
    print("\n🖥️  Installing desktop application dependencies...")
    failed_packages = []
    
    for package in desktop_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    return len(failed_packages) == 0, failed_packages

def create_virtual_environment():
    """Create a virtual environment for the project"""
    venv_path = "venv_crm"
    
    if os.path.exists(venv_path):
        print(f"📁 Virtual environment '{venv_path}' already exists")
        return True
    
    try:
        print(f"🔧 Creating virtual environment '{venv_path}'...")
        subprocess.check_call([sys.executable, "-m", "venv", venv_path])
        print(f"✅ Virtual environment created successfully")
        
        # Provide activation instructions
        if os.name == 'nt':  # Windows
            activate_script = os.path.join(venv_path, "Scripts", "activate.bat")
            print(f"\n📋 To activate the virtual environment, run:")
            print(f"   {activate_script}")
        else:  # Unix/Linux/Mac
            activate_script = os.path.join(venv_path, "bin", "activate")
            print(f"\n📋 To activate the virtual environment, run:")
            print(f"   source {activate_script}")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 Abu Alaa Stores CRM - Dependency Installer")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Ask user about virtual environment
    print("\n🤔 Do you want to create a virtual environment? (Recommended)")
    print("   This will isolate the project dependencies from your system Python.")
    choice = input("Create virtual environment? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        if not create_virtual_environment():
            print("⚠️  Continuing without virtual environment...")
    
    # Upgrade pip first
    print("\n⬆️  Upgrading pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip upgraded successfully")
    except subprocess.CalledProcessError:
        print("⚠️  Failed to upgrade pip, continuing...")
    
    # Install core dependencies
    core_success, core_failed = install_core_dependencies()
    
    # Install desktop dependencies
    desktop_success, desktop_failed = install_desktop_dependencies()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Installation Summary")
    print("=" * 50)
    
    if core_success and desktop_success:
        print("🎉 All dependencies installed successfully!")
        print("\n📋 Next Steps:")
        print("1. Run 'python build_desktop.py' to build the desktop application")
        print("2. Or run 'python app.py --desktop' to test the application")
        print("3. Or run 'python desktop_launcher.py' for the GUI launcher")
    else:
        print("⚠️  Some dependencies failed to install:")
        if core_failed:
            print("Core dependencies that failed:")
            for pkg in core_failed:
                print(f"  - {pkg}")
        if desktop_failed:
            print("Desktop dependencies that failed:")
            for pkg in desktop_failed:
                print(f"  - {pkg}")
        
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you have internet connection")
        print("2. Try running as administrator")
        print("3. Update pip: python -m pip install --upgrade pip")
        print("4. Install packages manually: pip install <package_name>")
    
    print("\n📞 For support, contact the development team.")
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
