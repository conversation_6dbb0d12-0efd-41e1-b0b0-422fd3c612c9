import os
import subprocess
import sys

def build_exe_simple():
    """بناء ملف تنفيذي للويندوز بطريقة مبسطة"""
    
    print("🚀 جاري بناء ملف تنفيذي للويندوز...")
    print("هذا قد يستغرق بضع دقائق...")
    
    # إنشاء مجلد البناء
    build_dir = 'build_exe'
    if not os.path.exists(build_dir):
        os.makedirs(build_dir)
    
    # إنشاء مجلد uploads إذا لم يكن موجوداً
    if not os.path.exists('uploads'):
        os.makedirs('uploads')
    
    # أمر PyInstaller المبسط
    cmd = [
        'pyinstaller',
        '--onefile',
        '--noconsole',
        '--name=محلات_أبو_علاء_CRM',
        '--distpath=build_exe',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=uploads;uploads',
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--hidden-import=openpyxl',
        '--hidden-import=reportlab',
        '--hidden-import=qrcode',
        '--hidden-import=PIL',
        '--hidden-import=bcrypt',
        '--hidden-import=jwt',
        '--clean',
        'app.py'
    ]
    
    try:
        # تشغيل الأمر
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("\n✅ تم بناء الملف التنفيذي بنجاح!")
            print(f"📁 الملف موجود في: {build_dir}/محلات_أبو_علاء_CRM.exe")
            
            # إنشاء ملف README للمستخدم
            readme_content = """# محلات أبو علاء CRM - ملف تنفيذي للويندوز

## كيفية الاستخدام:

1. **تشغيل التطبيق:**
   - انقر مرتين على ملف "محلات_أبو_علاء_CRM.exe"
   - انتظر بضع ثوان حتى يبدأ التطبيق

2. **الوصول للتطبيق:**
   - افتح المتصفح واذهب إلى: http://127.0.0.1:5000
   - أو: http://localhost:5000

3. **بيانات الدخول:**
   - اسم المستخدم: admin
   - كلمة المرور: admin123

## ملاحظات مهمة:
- تأكد من أن جدار الحماية لا يمنع التطبيق
- إذا لم يعمل، جرب تشغيل الملف كمدير (Run as Administrator)
- قاعدة البيانات ستُنشأ تلقائياً في نفس مجلد الملف التنفيذي

## استكشاف الأخطاء:
- إذا لم يفتح المتصفح تلقائياً، افتحه يدوياً واذهب للرابط أعلاه
- تأكد من عدم تشغيل تطبيق آخر على المنفذ 5000

تم التطوير بواسطة: خالد شجاع
"""
            
            with open(f'{build_dir}/README.txt', 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print("\n📋 تم إنشاء ملف README.txt مع التعليمات")
            print("\n🎉 التطبيق جاهز للاستخدام!")
            
        else:
            print(f"\n❌ حدث خطأ أثناء البناء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe_simple() 