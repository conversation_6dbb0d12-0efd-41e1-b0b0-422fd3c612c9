using Microsoft.EntityFrameworkCore;
using CRMDesktopApp.Models;
using System;
using System.IO;

namespace CRMDesktopApp.Data
{
    public class CRMDbContext : DbContext
    {
        public CRMDbContext(DbContextOptions<CRMDbContext> options) : base(options)
        {
        }

        public CRMDbContext() : base()
        {
        }

        // DbSets for all entities
        public DbSet<User> Users { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Communication> Communications { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<Reminder> Reminders { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                try
                {
                    // Get the application directory first (next to exe)
                    string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                    string dbPath = Path.Combine(appDirectory, "CRM_Database.db");

                    // If that fails, try AppData
                    if (!Directory.Exists(appDirectory) || !HasWritePermission(appDirectory))
                    {
                        string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                        string appFolder = Path.Combine(appDataPath, "AbuAlaaStoresCRM");

                        // Create directory if it doesn't exist
                        if (!Directory.Exists(appFolder))
                        {
                            Directory.CreateDirectory(appFolder);
                        }

                        dbPath = Path.Combine(appFolder, "CRM_Database.db");
                    }

                    optionsBuilder.UseSqlite($"Data Source={dbPath}");
                    optionsBuilder.EnableSensitiveDataLogging(false);
                    optionsBuilder.EnableServiceProviderCaching(true);
                }
                catch (Exception ex)
                {
                    // Fallback to current directory
                    string fallbackPath = Path.Combine(Directory.GetCurrentDirectory(), "CRM_Database.db");
                    optionsBuilder.UseSqlite($"Data Source={fallbackPath}");
                    System.Diagnostics.Debug.WriteLine($"Database path error: {ex.Message}, using fallback: {fallbackPath}");
                }
            }
        }

        private bool HasWritePermission(string path)
        {
            try
            {
                string testFile = Path.Combine(path, "test_write.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Username).IsRequired().HasMaxLength(80);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(120);
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.Role).HasMaxLength(20).HasDefaultValue("مستخدم");
                entity.Property(e => e.FullName).HasMaxLength(100);
                entity.Property(e => e.Pin).HasMaxLength(6);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Phone);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Phone).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Email).HasMaxLength(120);
                entity.Property(e => e.NationalId).HasMaxLength(20);
                entity.Property(e => e.CompanyName).HasMaxLength(100);
                entity.Property(e => e.Category).HasMaxLength(50).HasDefaultValue("عادي");
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("نشط");
                entity.Property(e => e.TotalBalance).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.PendingAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");

                // Foreign key relationship
                entity.HasOne(e => e.User)
                      .WithMany(u => u.Customers)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Transaction entity
            modelBuilder.Entity<Transaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TransactionType).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("IQD");
                entity.Property(e => e.ReceiptNumber).HasMaxLength(50);
                entity.Property(e => e.TransactionDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Transactions)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany(u => u.Transactions)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Payment entity
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("IQD");
                entity.Property(e => e.ReceiptNumber).HasMaxLength(50);
                entity.Property(e => e.PaymentDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Payments)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany(u => u.Payments)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Communication entity
            modelBuilder.Entity<Communication>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasMaxLength(20);
                entity.Property(e => e.Timestamp).HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Communications)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany(u => u.Communications)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Document entity
            modelBuilder.Entity<Document>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Filename).IsRequired().HasMaxLength(255);
                entity.Property(e => e.OriginalFilename).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FileType).HasMaxLength(50);
                entity.Property(e => e.UploadDate).HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Documents)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany(u => u.Documents)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Reminder entity
            modelBuilder.Entity<Reminder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("معلق");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Reminders)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany(u => u.Reminders)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    Role = "مدير",
                    FullName = "مدير النظام",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            );

            // Seed sample customer categories
            var sampleCustomers = new[]
            {
                new Customer
                {
                    Id = 1,
                    Name = "زبون تجريبي",
                    Phone = "07701234567",
                    Email = "<EMAIL>",
                    Category = "عادي",
                    Status = "نشط",
                    UserId = 1,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            };

            modelBuilder.Entity<Customer>().HasData(sampleCustomers);
        }

        public async Task<bool> EnsureDatabaseCreatedAsync()
        {
            try
            {
                await Database.EnsureCreatedAsync();
                return true;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Database creation error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                return await Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }
    }
}
