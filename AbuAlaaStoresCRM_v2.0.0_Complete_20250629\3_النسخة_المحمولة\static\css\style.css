/* Arabic RTL Support and Dark Theme */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    direction: rtl;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #2d2d2d;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4CAF50;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.nav-menu a:hover {
    background: #404040;
}

/* Footer */
.footer {
    background: #2d2d2d;
    padding: 1rem 2rem;
    margin-top: 3rem;
    text-align: center;
    border-top: 1px solid #444;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-content p {
    color: #cccccc;
    margin: 0;
}

.developer-link {
    color: #4CAF50;
    text-decoration: none;
    font-weight: bold;
}

.developer-link:hover {
    text-decoration: underline;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #666;
    color: white;
}

.btn-secondary:hover {
    background: #555;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #da190b;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #cccccc;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #ffffff;
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Dashboard */
.dashboard {
    padding: 2rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #4CAF50;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #2d2d2d;
    padding: 1.5rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-content h3 {
    font-size: 1.8rem;
    margin-bottom: 0.25rem;
    color: #4CAF50;
}

.stat-content p {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Dashboard Sections */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-section {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 1.2rem;
    color: #4CAF50;
}

/* Transactions List */
.transactions-list {
    max-height: 400px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #444;
    transition: background 0.3s;
}

.transaction-item:hover {
    background: #333;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.transaction-icon .text-success {
    color: #4CAF50;
}

.transaction-icon .text-danger {
    color: #f44336;
}

.transaction-details h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.transaction-details p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.transaction-details small {
    color: #888;
    font-size: 0.8rem;
}

.transaction-amount .amount {
    font-weight: bold;
    font-size: 1.1rem;
}

.amount.positive {
    color: #4CAF50;
}

.amount.negative {
    color: #f44336;
}

/* Customers List */
.customers-list {
    max-height: 400px;
    overflow-y: auto;
}

.customer-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #444;
    transition: background 0.3s;
}

.customer-item:hover {
    background: #333;
}

.customer-avatar {
    width: 40px;
    height: 40px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.customer-details h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.customer-details p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.customer-details small {
    color: #888;
    font-size: 0.8rem;
}

.customer-balance .balance {
    font-weight: bold;
    font-size: 1.1rem;
}

.balance.pending {
    color: #ff9800;
}

/* Quick Actions */
.quick-actions {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 1.5rem;
}

.quick-actions h2 {
    margin-bottom: 1rem;
    color: #4CAF50;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: #333;
    padding: 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    color: white;
    text-align: center;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.action-card:hover {
    background: #404040;
    transform: translateY(-2px);
}

.action-card i {
    font-size: 2rem;
    color: #4CAF50;
}

/* Reports Page */
.reports-page {
    padding: 2rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.report-card {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s;
}

.report-card:hover {
    transform: translateY(-5px);
}

.report-icon {
    width: 80px;
    height: 80px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.report-content h3 {
    color: #4CAF50;
    margin-bottom: 0.5rem;
}

.report-content p {
    color: #cccccc;
    margin-bottom: 1rem;
}

.report-actions {
    margin-top: 1rem;
}

.reports-section {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 2rem;
}

.reports-section h2 {
    color: #4CAF50;
    margin-bottom: 1.5rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #2d2d2d;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #4CAF50;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #ffffff;
}

.modal form {
    padding: 1.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Auth Pages */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.auth-card {
    background: #2d2d2d;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    width: 100%;
    max-width: 400px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: 1rem;
}

.auth-header h2 {
    margin-bottom: 0.5rem;
    color: #4CAF50;
}

.auth-header p {
    color: #cccccc;
}

.auth-form {
    margin-bottom: 1.5rem;
}

.auth-footer {
    text-align: center;
    color: #cccccc;
}

.auth-footer a {
    color: #4CAF50;
    text-decoration: none;
}

/* Customers Page */
.customers-page {
    padding: 2rem;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #4CAF50;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #cccccc;
}

.filters-section {
    background: #2d2d2d;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.filters-form {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #ffffff;
}

.search-box button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
}

.filter-options {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.filter-options select {
    padding: 0.75rem;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #ffffff;
    min-width: 150px;
}

/* Customer Cards */
.customer-card {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
}

.customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.customer-avatar {
    width: 50px;
    height: 50px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.customer-details h3 {
    margin-bottom: 0.5rem;
    color: #4CAF50;
}

.customer-phone,
.customer-email {
    color: #cccccc;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.customer-meta {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.category,
.status {
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.category {
    background: #4CAF50;
    color: white;
}

.status {
    background: #666;
    color: white;
}

.status-نشط {
    background: #4CAF50;
}

.status-غير نشط {
    background: #ff9800;
}

.status-محظور {
    background: #f44336;
}

.customer-balance {
    text-align: center;
}

.balance-item {
    margin-bottom: 0.5rem;
}

.balance-item .label {
    color: #cccccc;
    font-size: 0.9rem;
}

.balance-item .amount {
    font-weight: bold;
    font-size: 1.1rem;
    margin-right: 0.5rem;
}

.amount.paid {
    color: #4CAF50;
}

.amount.pending {
    color: #ff9800;
}

.customer-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Form Pages */
.form-page {
    padding: 2rem;
}

.form-container {
    background: #2d2d2d;
    border-radius: 10px;
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #444;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #4CAF50;
    margin-bottom: 1rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.page-link {
    padding: 0.5rem 1rem;
    background: #333;
    color: #ffffff;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.page-link:hover {
    background: #4CAF50;
}

.page-info {
    color: #cccccc;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #888;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #cccccc;
}

/* Notifications */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-success {
    background: #4CAF50;
    color: white;
}

.alert-error {
    background: #f44336;
    color: white;
}

.alert-info {
    background: #2196F3;
    color: white;
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .customer-card {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filters-form {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filter-options {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .modal,
    .footer {
        display: none !important;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .stat-card,
    .dashboard-section,
    .customer-card {
        background: white;
        border: 1px solid #ddd;
        color: black;
    }
} 