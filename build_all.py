#!/usr/bin/env python3
"""
Master Build Script for Abu Alaa Stores CRM Desktop Application
Builds all variants: Flask+PyInstaller, Electron, and .NET
"""

import subprocess
import os
import sys
import shutil
import json
import time
from datetime import datetime

class BuildManager:
    def __init__(self):
        self.build_log = []
        self.start_time = time.time()
        self.builds_completed = []
        self.builds_failed = []
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        print(log_entry)
    
    def run_command(self, command, description, cwd=None):
        """Run a command and log the result"""
        self.log(f"Starting: {description}")
        try:
            if isinstance(command, str):
                result = subprocess.run(command, shell=True, cwd=cwd, 
                                      capture_output=True, text=True, check=True)
            else:
                result = subprocess.run(command, cwd=cwd, 
                                      capture_output=True, text=True, check=True)
            
            self.log(f"Completed: {description}", "SUCCESS")
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            self.log(f"Failed: {description} - {e}", "ERROR")
            if e.stderr:
                self.log(f"Error details: {e.stderr}", "ERROR")
            return False, e.stderr
    
    def check_prerequisites(self):
        """Check if all required tools are installed"""
        self.log("Checking prerequisites...")
        
        prerequisites = {
            'python': ['python', '--version'],
            'pip': ['pip', '--version'],
            'node': ['node', '--version'],
            'npm': ['npm', '--version'],
            'dotnet': ['dotnet', '--version']
        }
        
        available_tools = {}
        
        for tool, command in prerequisites.items():
            try:
                result = subprocess.run(command, capture_output=True, text=True, check=True)
                version = result.stdout.strip()
                available_tools[tool] = version
                self.log(f"✅ {tool}: {version}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.log(f"❌ {tool}: Not found")
                available_tools[tool] = None
        
        return available_tools
    
    def build_flask_pyinstaller(self):
        """Build Flask + PyInstaller version"""
        self.log("=" * 50)
        self.log("Building Flask + PyInstaller Version")
        self.log("=" * 50)
        
        try:
            # Install Python dependencies
            success, output = self.run_command(
                [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                "Installing Python dependencies"
            )
            if not success:
                return False
            
            # Build executable
            success, output = self.run_command(
                [sys.executable, 'build_exe.py'],
                "Building Flask executable"
            )
            
            if success:
                self.builds_completed.append("Flask + PyInstaller")
                return True
            else:
                self.builds_failed.append("Flask + PyInstaller")
                return False
                
        except Exception as e:
            self.log(f"Flask build error: {e}", "ERROR")
            self.builds_failed.append("Flask + PyInstaller")
            return False
    
    def build_desktop_launcher(self):
        """Build desktop launcher version"""
        self.log("=" * 50)
        self.log("Building Desktop Launcher Version")
        self.log("=" * 50)
        
        try:
            # Install additional dependencies
            success, output = self.run_command(
                [sys.executable, '-m', 'pip', 'install', 'pystray', 'Pillow'],
                "Installing desktop dependencies"
            )
            if not success:
                return False
            
            # Build desktop version
            success, output = self.run_command(
                [sys.executable, 'build_desktop.py'],
                "Building desktop launcher"
            )
            
            if success:
                self.builds_completed.append("Desktop Launcher")
                return True
            else:
                self.builds_failed.append("Desktop Launcher")
                return False
                
        except Exception as e:
            self.log(f"Desktop launcher build error: {e}", "ERROR")
            self.builds_failed.append("Desktop Launcher")
            return False
    
    def build_electron(self):
        """Build Electron version"""
        self.log("=" * 50)
        self.log("Building Electron Version")
        self.log("=" * 50)
        
        try:
            # Check if package.json exists
            if not os.path.exists('package.json'):
                self.log("package.json not found, skipping Electron build", "WARNING")
                return False
            
            # Install npm dependencies
            success, output = self.run_command(
                ['npm', 'install'],
                "Installing npm dependencies"
            )
            if not success:
                return False
            
            # Build Electron app
            success, output = self.run_command(
                ['npm', 'run', 'dist'],
                "Building Electron application"
            )
            
            if success:
                self.builds_completed.append("Electron")
                return True
            else:
                self.builds_failed.append("Electron")
                return False
                
        except Exception as e:
            self.log(f"Electron build error: {e}", "ERROR")
            self.builds_failed.append("Electron")
            return False
    
    def build_dotnet(self):
        """Build .NET version"""
        self.log("=" * 50)
        self.log("Building .NET Version")
        self.log("=" * 50)
        
        try:
            # Check if .csproj exists
            csproj_files = [f for f in os.listdir('.') if f.endswith('.csproj')]
            if not csproj_files:
                self.log("No .csproj file found, skipping .NET build", "WARNING")
                return False
            
            # Restore packages
            success, output = self.run_command(
                ['dotnet', 'restore'],
                "Restoring .NET packages"
            )
            if not success:
                return False
            
            # Build release
            success, output = self.run_command(
                ['dotnet', 'build', '--configuration', 'Release'],
                "Building .NET application"
            )
            if not success:
                return False
            
            # Publish self-contained
            success, output = self.run_command(
                ['dotnet', 'publish', '--configuration', 'Release', 
                 '--runtime', 'win-x64', '--self-contained', 'true', 
                 '--output', 'publish_dotnet'],
                "Publishing .NET self-contained"
            )
            
            if success:
                self.builds_completed.append(".NET")
                return True
            else:
                self.builds_failed.append(".NET")
                return False
                
        except Exception as e:
            self.log(f".NET build error: {e}", "ERROR")
            self.builds_failed.append(".NET")
            return False
    
    def create_distribution_package(self):
        """Create a comprehensive distribution package"""
        self.log("=" * 50)
        self.log("Creating Distribution Package")
        self.log("=" * 50)
        
        dist_dir = 'distribution'
        if os.path.exists(dist_dir):
            shutil.rmtree(dist_dir)
        os.makedirs(dist_dir)
        
        # Copy successful builds
        if "Flask + PyInstaller" in self.builds_completed:
            flask_dir = os.path.join(dist_dir, 'Flask_PyInstaller')
            os.makedirs(flask_dir, exist_ok=True)
            if os.path.exists('dist'):
                shutil.copytree('dist', os.path.join(flask_dir, 'dist'), dirs_exist_ok=True)
        
        if "Desktop Launcher" in self.builds_completed:
            desktop_dir = os.path.join(dist_dir, 'Desktop_Launcher')
            os.makedirs(desktop_dir, exist_ok=True)
            if os.path.exists('dist_desktop'):
                shutil.copytree('dist_desktop', os.path.join(desktop_dir, 'dist'), dirs_exist_ok=True)
        
        if "Electron" in self.builds_completed:
            electron_dir = os.path.join(dist_dir, 'Electron')
            os.makedirs(electron_dir, exist_ok=True)
            if os.path.exists('dist_electron'):
                shutil.copytree('dist_electron', os.path.join(electron_dir, 'dist'), dirs_exist_ok=True)
        
        if ".NET" in self.builds_completed:
            dotnet_dir = os.path.join(dist_dir, 'DotNet')
            os.makedirs(dotnet_dir, exist_ok=True)
            if os.path.exists('publish_dotnet'):
                shutil.copytree('publish_dotnet', os.path.join(dotnet_dir, 'publish'), dirs_exist_ok=True)
        
        # Create comprehensive README
        self.create_distribution_readme(dist_dir)
        
        # Create launcher scripts
        self.create_launcher_scripts(dist_dir)
        
        self.log(f"Distribution package created: {os.path.abspath(dist_dir)}")
    
    def create_distribution_readme(self, dist_dir):
        """Create comprehensive README for distribution"""
        readme_content = f'''# Abu Alaa Stores CRM - Distribution Package

Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Build Duration: {time.time() - self.start_time:.1f} seconds

## Available Versions

'''
        
        for build in self.builds_completed:
            if build == "Flask + PyInstaller":
                readme_content += '''### Flask + PyInstaller Version
- **Location**: Flask_PyInstaller/dist/
- **Type**: Single executable file
- **Pros**: Simple deployment, no additional runtime required
- **Cons**: Larger file size, web-based UI
- **Best for**: Quick deployment, users familiar with web interfaces

'''
            elif build == "Desktop Launcher":
                readme_content += '''### Desktop Launcher Version
- **Location**: Desktop_Launcher/dist/
- **Type**: GUI launcher with system tray support
- **Pros**: Better user experience, system integration
- **Cons**: Requires Python runtime on target system
- **Best for**: Power users, better desktop integration

'''
            elif build == "Electron":
                readme_content += '''### Electron Version
- **Location**: Electron/dist/
- **Type**: Native desktop application with installer
- **Pros**: Modern UI, cross-platform, professional appearance
- **Cons**: Larger size, higher memory usage
- **Best for**: Professional deployment, modern user experience

'''
            elif build == ".NET":
                readme_content += '''### .NET Version
- **Location**: DotNet/publish/
- **Type**: Native Windows application
- **Pros**: True native performance, small size, Windows integration
- **Cons**: Windows-only, requires complete rewrite for other platforms
- **Best for**: Windows-only deployment, maximum performance

'''
        
        readme_content += f'''
## Failed Builds

'''
        for build in self.builds_failed:
            readme_content += f"- {build}: Build failed (check build log for details)\n"
        
        readme_content += '''
## Installation Instructions

### System Requirements
- Windows 7 or later (64-bit recommended)
- 4GB RAM minimum (8GB recommended)
- 200MB free disk space
- Administrator privileges for installation

### Quick Start
1. Choose the version that best fits your needs
2. Follow the specific installation instructions in each folder
3. Default login: admin / admin123
4. Change the password after first login

### Support
For technical support, contact the development team.

## Version History
- v2.0.0: Multi-platform desktop application with modern UI
- v1.0.0: Initial Flask web application

## License
This software is proprietary to Abu Alaa Stores.
'''
        
        with open(os.path.join(dist_dir, 'README.txt'), 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def create_launcher_scripts(self, dist_dir):
        """Create launcher scripts for each version"""
        # Main launcher script
        launcher_script = '''@echo off
title Abu Alaa Stores CRM - Launcher
echo.
echo ========================================
echo    Abu Alaa Stores CRM - Launcher
echo ========================================
echo.
echo Choose your preferred version:
echo.
'''
        
        option = 1
        for build in self.builds_completed:
            launcher_script += f"echo {option}. {build}\n"
            option += 1
        
        launcher_script += '''echo.
set /p choice="Enter your choice (1-''' + str(len(self.builds_completed)) + '''): "
echo.

'''
        
        option = 1
        for build in self.builds_completed:
            launcher_script += f'''if "%choice%"=="{option}" (
    echo Starting {build}...
    cd /d "%~dp0\\{build.replace(" ", "_").replace("+", "")}"
    call start.bat
    goto end
)
'''
            option += 1
        
        launcher_script += '''
echo Invalid choice!
pause
:end
'''
        
        with open(os.path.join(dist_dir, 'Launch_CRM.bat'), 'w') as f:
            f.write(launcher_script)
    
    def save_build_log(self):
        """Save build log to file"""
        log_content = "\n".join(self.build_log)
        with open('build_log.txt', 'w', encoding='utf-8') as f:
            f.write(log_content)
        self.log(f"Build log saved to: {os.path.abspath('build_log.txt')}")
    
    def print_summary(self):
        """Print build summary"""
        duration = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("BUILD SUMMARY")
        print("=" * 60)
        print(f"Total build time: {duration:.1f} seconds")
        print(f"Successful builds: {len(self.builds_completed)}")
        print(f"Failed builds: {len(self.builds_failed)}")
        
        if self.builds_completed:
            print("\n✅ Successful builds:")
            for build in self.builds_completed:
                print(f"  - {build}")
        
        if self.builds_failed:
            print("\n❌ Failed builds:")
            for build in self.builds_failed:
                print(f"  - {build}")
        
        if self.builds_completed:
            print(f"\n📁 Distribution package: {os.path.abspath('distribution')}")
        
        print(f"\n📝 Build log: {os.path.abspath('build_log.txt')}")

def main():
    """Main build function"""
    builder = BuildManager()
    
    print("🚀 Abu Alaa Stores CRM - Master Build Script")
    print("=" * 60)
    
    # Check prerequisites
    available_tools = builder.check_prerequisites()
    
    # Ask user what to build
    print("\n🤔 What would you like to build?")
    print("1. Flask + PyInstaller only")
    print("2. Desktop Launcher only")
    print("3. Electron only (requires Node.js)")
    print("4. .NET only (requires .NET SDK)")
    print("5. All available versions")
    print("6. Custom selection")
    
    choice = input("Enter your choice (1-6): ").strip()
    
    builds_to_run = []
    
    if choice == '1':
        builds_to_run = ['flask']
    elif choice == '2':
        builds_to_run = ['desktop']
    elif choice == '3':
        builds_to_run = ['electron']
    elif choice == '4':
        builds_to_run = ['dotnet']
    elif choice == '5':
        builds_to_run = ['flask', 'desktop', 'electron', 'dotnet']
    elif choice == '6':
        print("\nSelect builds to run (y/n):")
        if input("Flask + PyInstaller? (y/n): ").lower().startswith('y'):
            builds_to_run.append('flask')
        if input("Desktop Launcher? (y/n): ").lower().startswith('y'):
            builds_to_run.append('desktop')
        if available_tools['node'] and input("Electron? (y/n): ").lower().startswith('y'):
            builds_to_run.append('electron')
        if available_tools['dotnet'] and input(".NET? (y/n): ").lower().startswith('y'):
            builds_to_run.append('dotnet')
    else:
        print("❌ Invalid choice")
        return
    
    # Run selected builds
    if 'flask' in builds_to_run:
        builder.build_flask_pyinstaller()
    
    if 'desktop' in builds_to_run:
        builder.build_desktop_launcher()
    
    if 'electron' in builds_to_run and available_tools['node']:
        builder.build_electron()
    
    if 'dotnet' in builds_to_run and available_tools['dotnet']:
        builder.build_dotnet()
    
    # Create distribution package
    if builder.builds_completed:
        builder.create_distribution_package()
    
    # Save build log and print summary
    builder.save_build_log()
    builder.print_summary()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
