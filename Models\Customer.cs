using System;
using System.ComponentModel.DataAnnotations;

namespace CRMDesktopApp.Models
{
    public class Customer
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Customer name is required")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string Phone { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Invalid email address")]
        [StringLength(120, ErrorMessage = "Email cannot exceed 120 characters")]
        public string? Email { get; set; }

        [StringLength(20, ErrorMessage = "National ID cannot exceed 20 characters")]
        public string? NationalId { get; set; }

        public string? Address { get; set; }

        [StringLength(100, ErrorMessage = "Company name cannot exceed 100 characters")]
        public string? CompanyName { get; set; }

        [StringLength(50, ErrorMessage = "Category cannot exceed 50 characters")]
        public string Category { get; set; } = "عادي";

        [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
        public string Status { get; set; } = "نشط";

        public decimal TotalBalance { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public decimal PendingAmount { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        public string? CustomFields { get; set; } // JSON string
        public string? Notes { get; set; }

        public int UserId { get; set; }

        // Navigation properties
        public User? User { get; set; }
        public List<Transaction> Transactions { get; set; } = new List<Transaction>();
        public List<Payment> Payments { get; set; } = new List<Payment>();
        public List<Communication> Communications { get; set; } = new List<Communication>();
        public List<Document> Documents { get; set; } = new List<Document>();
        public List<Reminder> Reminders { get; set; } = new List<Reminder>();

        // Computed properties
        public string DisplayName => !string.IsNullOrEmpty(CompanyName) ? $"{Name} ({CompanyName})" : Name;
        public string StatusDisplay => Status == "نشط" ? "Active" : "Inactive";
        public string CategoryDisplay => Category;
        public bool HasPendingAmount => PendingAmount > 0;
        public string FormattedBalance => $"{TotalBalance:C}";
        public string FormattedPending => $"{PendingAmount:C}";
    }

    public class User
    {
        public int Id { get; set; }

        [Required]
        [StringLength(80)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(120)]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string PasswordHash { get; set; } = string.Empty;

        [StringLength(20)]
        public string Role { get; set; } = "user";

        [StringLength(6)]
        public string? Pin { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastLogin { get; set; }

        // Navigation properties
        public List<Customer> Customers { get; set; } = new List<Customer>();
        public List<Transaction> Transactions { get; set; } = new List<Transaction>();
        public List<Payment> Payments { get; set; } = new List<Payment>();
    }

    public class Transaction
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }

        [Required]
        [StringLength(20)]
        public string TransactionType { get; set; } = string.Empty; // 'بيع' or 'تسديد'

        [Required]
        public decimal Amount { get; set; }

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(3)]
        public string Currency { get; set; } = "IQD";

        public string? Notes { get; set; }

        [StringLength(50)]
        public string? ReceiptNumber { get; set; }

        public int UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
    }

    public class Payment
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(3)]
        public string Currency { get; set; } = "IQD";

        public string? Notes { get; set; }

        [StringLength(50)]
        public string? ReceiptNumber { get; set; }

        public int UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
    }

    public class Communication
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }

        [StringLength(20)]
        public string? Type { get; set; } // call, message, email, meeting

        public string? Content { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public int UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
    }

    public class Document
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }

        [Required]
        [StringLength(255)]
        public string Filename { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFilename { get; set; } = string.Empty;

        [StringLength(50)]
        public string? FileType { get; set; }

        public DateTime UploadDate { get; set; } = DateTime.Now;
        public int UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
    }

    public class Reminder
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "معلق";

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public int UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
    }
}
