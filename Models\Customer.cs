using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRMDesktopApp.Models
{
    public class Customer
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الزبون مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "اسم الزبون")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 رقم")]
        [Display(Name = "رقم الهاتف")]
        public string Phone { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "عنوان بريد إلكتروني غير صحيح")]
        [StringLength(120, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 120 حرف")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهوية لا يمكن أن يتجاوز 20 رقم")]
        [Display(Name = "رقم الهوية")]
        public string? NationalId { get; set; }

        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(100, ErrorMessage = "اسم الشركة لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "اسم الشركة")]
        public string? CompanyName { get; set; }

        [StringLength(50, ErrorMessage = "الفئة لا يمكن أن تتجاوز 50 حرف")]
        [Display(Name = "فئة الزبون")]
        public string Category { get; set; } = "عادي";

        [StringLength(20, ErrorMessage = "الحالة لا يمكن أن تتجاوز 20 حرف")]
        [Display(Name = "حالة الزبون")]
        public string Status { get; set; } = "نشط";

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "إجمالي الرصيد")]
        public decimal TotalBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المدفوع")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المعلق")]
        public decimal PendingAmount { get; set; } = 0;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        [Display(Name = "حقول إضافية")]
        public string? CustomFields { get; set; } // JSON string

        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Communication> Communications { get; set; } = new List<Communication>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();

        // Computed properties
        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(CompanyName) ? $"{Name} ({CompanyName})" : Name;

        [NotMapped]
        public string StatusDisplay => Status == "نشط" ? "نشط" : "غير نشط";

        [NotMapped]
        public string CategoryDisplay => Category;

        [NotMapped]
        public bool HasPendingAmount => PendingAmount > 0;

        [NotMapped]
        public string FormattedBalance => $"{TotalBalance:N0} د.ع";

        [NotMapped]
        public string FormattedPending => $"{PendingAmount:N0} د.ع";

        [NotMapped]
        public string FormattedPaid => $"{PaidAmount:N0} د.ع";
    }

    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(80, ErrorMessage = "اسم المستخدم لا يمكن أن يتجاوز 80 حرف")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "عنوان بريد إلكتروني غير صحيح")]
        [StringLength(120, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 120 حرف")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [Display(Name = "كلمة المرور")]
        public string PasswordHash { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "الدور لا يمكن أن يتجاوز 20 حرف")]
        [Display(Name = "دور المستخدم")]
        public string Role { get; set; } = "مستخدم";

        [StringLength(6, ErrorMessage = "الرقم السري لا يمكن أن يتجاوز 6 أرقام")]
        [Display(Name = "الرقم السري")]
        public string? Pin { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLogin { get; set; }

        [Display(Name = "الاسم الكامل")]
        [StringLength(100)]
        public string? FullName { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Communication> Communications { get; set; } = new List<Communication>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();

        // Computed properties
        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(FullName) ? FullName : Username;

        [NotMapped]
        public string RoleDisplay => Role == "مدير" ? "مدير" : "مستخدم";

        [NotMapped]
        public string StatusDisplay => IsActive ? "نشط" : "غير نشط";
    }

    public class Transaction
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الزبون مطلوب")]
        [Display(Name = "الزبون")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        [StringLength(20, ErrorMessage = "نوع المعاملة لا يمكن أن يتجاوز 20 حرف")]
        [Display(Name = "نوع المعاملة")]
        public string TransactionType { get; set; } = string.Empty; // 'بيع' or 'شراء' or 'إرجاع'

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; }

        [Display(Name = "تاريخ المعاملة")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [StringLength(50, ErrorMessage = "طريقة الدفع لا يمكن أن تتجاوز 50 حرف")]
        [Display(Name = "طريقة الدفع")]
        public string? PaymentMethod { get; set; }

        [StringLength(3, ErrorMessage = "العملة لا يمكن أن تتجاوز 3 أحرف")]
        [Display(Name = "العملة")]
        public string Currency { get; set; } = "IQD";

        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [StringLength(50, ErrorMessage = "رقم الإيصال لا يمكن أن يتجاوز 50 حرف")]
        [Display(Name = "رقم الإيصال")]
        public string? ReceiptNumber { get; set; }

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // Computed properties
        [NotMapped]
        public string FormattedAmount => $"{Amount:N0} {Currency}";

        [NotMapped]
        public string TypeDisplay => TransactionType switch
        {
            "بيع" => "بيع",
            "شراء" => "شراء",
            "إرجاع" => "إرجاع",
            _ => TransactionType
        };

        [NotMapped]
        public string PaymentMethodDisplay => PaymentMethod ?? "غير محدد";
    }

    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الزبون مطلوب")]
        [Display(Name = "الزبون")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الدفع")]
        public decimal Amount { get; set; }

        [Display(Name = "تاريخ الدفع")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [StringLength(50, ErrorMessage = "طريقة الدفع لا يمكن أن تتجاوز 50 حرف")]
        [Display(Name = "طريقة الدفع")]
        public string? PaymentMethod { get; set; }

        [StringLength(3, ErrorMessage = "العملة لا يمكن أن تتجاوز 3 أحرف")]
        [Display(Name = "العملة")]
        public string Currency { get; set; } = "IQD";

        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [StringLength(50, ErrorMessage = "رقم الإيصال لا يمكن أن يتجاوز 50 حرف")]
        [Display(Name = "رقم الإيصال")]
        public string? ReceiptNumber { get; set; }

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // Computed properties
        [NotMapped]
        public string FormattedAmount => $"{Amount:N0} {Currency}";

        [NotMapped]
        public string PaymentMethodDisplay => PaymentMethod ?? "غير محدد";
    }

    public class Communication
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الزبون مطلوب")]
        [Display(Name = "الزبون")]
        public int CustomerId { get; set; }

        [StringLength(20, ErrorMessage = "نوع التواصل لا يمكن أن يتجاوز 20 حرف")]
        [Display(Name = "نوع التواصل")]
        public string? Type { get; set; } // مكالمة، رسالة، بريد، اجتماع

        [Display(Name = "المحتوى")]
        public string? Content { get; set; }

        [Display(Name = "وقت التواصل")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // Computed properties
        [NotMapped]
        public string TypeDisplay => Type switch
        {
            "مكالمة" => "مكالمة هاتفية",
            "رسالة" => "رسالة نصية",
            "بريد" => "بريد إلكتروني",
            "اجتماع" => "اجتماع شخصي",
            _ => Type ?? "غير محدد"
        };
    }

    public class Document
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الزبون مطلوب")]
        [Display(Name = "الزبون")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "اسم الملف مطلوب")]
        [StringLength(255, ErrorMessage = "اسم الملف لا يمكن أن يتجاوز 255 حرف")]
        [Display(Name = "اسم الملف")]
        public string Filename { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأصلي للملف مطلوب")]
        [StringLength(255, ErrorMessage = "الاسم الأصلي لا يمكن أن يتجاوز 255 حرف")]
        [Display(Name = "الاسم الأصلي")]
        public string OriginalFilename { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "نوع الملف لا يمكن أن يتجاوز 50 حرف")]
        [Display(Name = "نوع الملف")]
        public string? FileType { get; set; }

        [Display(Name = "تاريخ الرفع")]
        public DateTime UploadDate { get; set; } = DateTime.Now;

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        [Display(Name = "حجم الملف")]
        public long FileSize { get; set; }

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // Computed properties
        [NotMapped]
        public string FormattedFileSize => FileSize switch
        {
            < 1024 => $"{FileSize} بايت",
            < 1024 * 1024 => $"{FileSize / 1024:F1} كيلوبايت",
            < 1024 * 1024 * 1024 => $"{FileSize / (1024 * 1024):F1} ميجابايت",
            _ => $"{FileSize / (1024 * 1024 * 1024):F1} جيجابايت"
        };
    }

    public class Reminder
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الزبون مطلوب")]
        [Display(Name = "الزبون")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "عنوان التذكير مطلوب")]
        [StringLength(100, ErrorMessage = "العنوان لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "عنوان التذكير")]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
        [Display(Name = "تاريخ الاستحقاق")]
        public DateTime DueDate { get; set; }

        [StringLength(20, ErrorMessage = "الحالة لا يمكن أن تتجاوز 20 حرف")]
        [Display(Name = "حالة التذكير")]
        public string Status { get; set; } = "معلق";

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // Computed properties
        [NotMapped]
        public string StatusDisplay => Status switch
        {
            "معلق" => "معلق",
            "مكتمل" => "مكتمل",
            "ملغي" => "ملغي",
            _ => Status
        };

        [NotMapped]
        public bool IsOverdue => DueDate < DateTime.Now && Status == "معلق";

        [NotMapped]
        public string DueDateDisplay => DueDate.ToString("yyyy/MM/dd HH:mm");
    }
}
