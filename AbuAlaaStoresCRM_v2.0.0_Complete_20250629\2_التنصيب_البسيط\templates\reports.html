{% extends "base.html" %}

{% block title %}التقارير{% endblock %}

{% block content %}
<div class="reports-page">
    <div class="page-header">
        <h1>التقارير</h1>
        <p>عرض وتصدير تقارير الزبائن والمعاملات</p>
    </div>

    <div class="reports-grid">
        <div class="report-card">
            <div class="report-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="report-content">
                <h3>تقرير الزبائن</h3>
                <p>تقرير شامل بجميع بيانات الزبائن والأرصدة</p>
                <div class="report-actions">
                    <a href="{{ url_for('customer_report') }}" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        تصدير Excel
                    </a>
                </div>
            </div>
        </div>

        <div class="report-card">
            <div class="report-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div class="report-content">
                <h3>تقرير المبيعات</h3>
                <p>تقرير المبيعات والتسديدات الشهرية</p>
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="generateSalesReport()">
                        <i class="fas fa-download"></i>
                        تصدير Excel
                    </button>
                </div>
            </div>
        </div>

        <div class="report-card">
            <div class="report-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="report-content">
                <h3>تقرير الأرصدة</h3>
                <p>تقرير الأرصدة المعلقة والمدفوعات</p>
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="generateBalanceReport()">
                        <i class="fas fa-download"></i>
                        تصدير Excel
                    </button>
                </div>
            </div>
        </div>

        <div class="report-card">
            <div class="report-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="report-content">
                <h3>تقرير شهري</h3>
                <p>تقرير شامل للأنشطة الشهرية</p>
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="generateMonthlyReport()">
                        <i class="fas fa-download"></i>
                        تصدير Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="reports-section">
        <h2>إحصائيات سريعة</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalCustomers">-</h3>
                    <p>إجمالي الزبائن</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalSales">-</h3>
                    <p>إجمالي المبيعات</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalPending">-</h3>
                    <p>إجمالي المعلق</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3 id="avgTransaction">-</h3>
                    <p>متوسط المعاملة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load statistics on page load
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
});

function loadStatistics() {
    // This would typically fetch from an API endpoint
    // For now, we'll use placeholder data
    document.getElementById('totalCustomers').textContent = '0';
    document.getElementById('totalSales').textContent = '0 د.ع';
    document.getElementById('totalPending').textContent = '0 د.ع';
    document.getElementById('avgTransaction').textContent = '0 د.ع';
}

function generateSalesReport() {
    showNotification('جاري إنشاء تقرير المبيعات...', 'info');
    // This would typically call an API endpoint
    setTimeout(() => {
        showNotification('تم إنشاء تقرير المبيعات بنجاح', 'success');
    }, 2000);
}

function generateBalanceReport() {
    showNotification('جاري إنشاء تقرير الأرصدة...', 'info');
    // This would typically call an API endpoint
    setTimeout(() => {
        showNotification('تم إنشاء تقرير الأرصدة بنجاح', 'success');
    }, 2000);
}

function generateMonthlyReport() {
    showNotification('جاري إنشاء التقرير الشهري...', 'info');
    // This would typically call an API endpoint
    setTimeout(() => {
        showNotification('تم إنشاء التقرير الشهري بنجاح', 'success');
    }, 2000);
}
</script>
{% endblock %} 