using System;
using System.Drawing;
using System.Windows.Forms;

namespace CRMDesktopApp.Forms
{
    public partial class SplashForm : Form
    {
        private Label lblTitle;
        private Label lblSubtitle;
        private Label lblStatus;
        private ProgressBar progressBar;
        private PictureBox picLogo;
        private Panel pnlMain;

        public SplashForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Size = new Size(500, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.White;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Main panel with border
            pnlMain = new Panel
            {
                Size = new Size(498, 298),
                Location = new Point(1, 1),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Logo placeholder
            picLogo = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 30),
                BackColor = Color.FromArgb(0, 123, 255),
                SizeMode = PictureBoxSizeMode.CenterImage
            };

            // Create a simple logo
            var logoBitmap = new Bitmap(80, 80);
            using (var g = Graphics.FromImage(logoBitmap))
            {
                g.FillEllipse(new SolidBrush(Color.FromArgb(0, 123, 255)), 0, 0, 80, 80);
                g.DrawString("CRM", new Font("Tahoma", 16, FontStyle.Bold), 
                    Brushes.White, new RectangleF(0, 0, 80, 80), 
                    new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
            }
            picLogo.Image = logoBitmap;

            // Title
            lblTitle = new Label
            {
                Text = "محلات أبو علاء",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 30),
                Location = new Point(50, 120)
            };

            // Subtitle
            lblSubtitle = new Label
            {
                Text = "نظام إدارة الزبائن المتطور",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(108, 117, 125),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 25),
                Location = new Point(50, 155)
            };

            // Progress bar
            progressBar = new ProgressBar
            {
                Size = new Size(300, 20),
                Location = new Point(100, 200),
                Style = ProgressBarStyle.Marquee,
                MarqueeAnimationSpeed = 30
            };

            // Status label
            lblStatus = new Label
            {
                Text = "جاري تحميل التطبيق...",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(108, 117, 125),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 20),
                Location = new Point(50, 230)
            };

            // Version label
            var lblVersion = new Label
            {
                Text = "الإصدار 3.0.0",
                Font = new Font("Tahoma", 8),
                ForeColor = Color.FromArgb(173, 181, 189),
                TextAlign = ContentAlignment.BottomRight,
                Size = new Size(100, 15),
                Location = new Point(390, 270)
            };

            // Add controls to main panel
            pnlMain.Controls.AddRange(new Control[]
            {
                picLogo, lblTitle, lblSubtitle, progressBar, lblStatus, lblVersion
            });

            // Add main panel to form
            this.Controls.Add(pnlMain);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Add shadow effect
            AddShadowEffect();

            // Set region for rounded corners
            SetRoundedCorners();
        }

        private void AddShadowEffect()
        {
            // Create a shadow effect by drawing a darker rectangle behind the form
            this.Paint += (sender, e) =>
            {
                var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0));
                e.Graphics.FillRectangle(shadowBrush, 3, 3, this.Width - 3, this.Height - 3);
                shadowBrush.Dispose();
            };
        }

        private void SetRoundedCorners()
        {
            // Simple rounded corners effect
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            int radius = 10;
            
            path.AddArc(0, 0, radius, radius, 180, 90);
            path.AddArc(this.Width - radius, 0, radius, radius, 270, 90);
            path.AddArc(this.Width - radius, this.Height - radius, radius, radius, 0, 90);
            path.AddArc(0, this.Height - radius, radius, radius, 90, 90);
            path.CloseFigure();
            
            this.Region = new Region(path);
        }

        public void UpdateStatus(string status)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<string>(UpdateStatus), status);
                return;
            }

            lblStatus.Text = status;
            Application.DoEvents();
        }

        public void SetProgress(int percentage)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int>(SetProgress), percentage);
                return;
            }

            if (progressBar.Style != ProgressBarStyle.Continuous)
            {
                progressBar.Style = ProgressBarStyle.Continuous;
            }

            progressBar.Value = Math.Min(Math.Max(percentage, 0), 100);
            Application.DoEvents();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            
            // Animate the form appearance
            AnimateShow();
        }

        private void AnimateShow()
        {
            // Simple fade-in animation
            this.Opacity = 0;
            var timer = new System.Windows.Forms.Timer { Interval = 50 };
            timer.Tick += (s, e) =>
            {
                this.Opacity += 0.1;
                if (this.Opacity >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            timer.Start();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Animate the form disappearance
            if (!e.Cancel)
            {
                AnimateHide();
            }
            base.OnFormClosing(e);
        }

        private void AnimateHide()
        {
            var timer = new Timer { Interval = 30 };
            timer.Tick += (s, e) =>
            {
                this.Opacity -= 0.15;
                if (this.Opacity <= 0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            timer.Start();
        }

        // Prevent the form from being moved
        protected override void WndProc(ref Message m)
        {
            const int WM_NCHITTEST = 0x84;
            const int HTCLIENT = 1;
            const int HTCAPTION = 2;

            if (m.Msg == WM_NCHITTEST)
            {
                base.WndProc(ref m);
                if ((int)m.Result == HTCLIENT)
                    m.Result = (IntPtr)HTCAPTION;
                return;
            }
            base.WndProc(ref m);
        }
    }
}
