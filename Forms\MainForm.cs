using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using CRMDesktopApp.Models;
using CRMDesktopApp.Services;
using Microsoft.Extensions.DependencyInjection;

namespace CRMDesktopApp.Forms
{
    public partial class MainForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly IServiceProvider _serviceProvider;
        private User _currentUser;

        // UI Controls
        private MenuStrip menuStrip;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private Panel pnlSidebar;
        private Panel pnlMain;
        private Label lblWelcome;
        private Label lblStats;

        // Menu items
        private ToolStripMenuItem mnuCustomers;
        private ToolStripMenuItem mnuTransactions;
        private ToolStripMenuItem mnuReports;
        private ToolStripMenuItem mnuSettings;
        private ToolStripMenuItem mnuHelp;

        // Toolbar buttons
        private ToolStripButton btnAddCustomer;
        private ToolStripButton btnAddTransaction;
        private ToolStripButton btnReports;
        private ToolStripButton btnBackup;

        // Sidebar buttons
        private Button btnCustomers;
        private Button btnTransactions;
        private Button btnPayments;
        private Button btnReminders;
        private Button btnDocuments;
        private Button btnReportsBtn;

        public MainForm(IDatabaseService databaseService, IServiceProvider serviceProvider, User currentUser)
        {
            _databaseService = databaseService;
            _serviceProvider = serviceProvider;
            _currentUser = currentUser;
            
            InitializeComponent();
            SetupForm();
            LoadDashboardAsync();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = $"نظام إدارة الزبائن - محلات أبو علاء | {_currentUser.DisplayName}";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Create menu strip
            CreateMenuStrip();

            // Create toolbar
            CreateToolStrip();

            // Create status strip
            CreateStatusStrip();

            // Create sidebar
            CreateSidebar();

            // Create main panel
            CreateMainPanel();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip
            {
                BackColor = Color.FromArgb(52, 58, 64),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10)
            };

            // Customers menu
            mnuCustomers = new ToolStripMenuItem("الزبائن");
            mnuCustomers.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("عرض جميع الزبائن", null, MnuViewCustomers_Click),
                new ToolStripMenuItem("إضافة زبون جديد", null, MnuAddCustomer_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("البحث عن زبون", null, MnuSearchCustomer_Click)
            });

            // Transactions menu
            mnuTransactions = new ToolStripMenuItem("المعاملات");
            mnuTransactions.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("عرض المعاملات", null, MnuViewTransactions_Click),
                new ToolStripMenuItem("إضافة معاملة جديدة", null, MnuAddTransaction_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("المدفوعات", null, MnuPayments_Click)
            });

            // Reports menu
            mnuReports = new ToolStripMenuItem("التقارير");
            mnuReports.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("تقرير الزبائن", null, MnuCustomerReport_Click),
                new ToolStripMenuItem("تقرير المبيعات", null, MnuSalesReport_Click),
                new ToolStripMenuItem("تقرير المدفوعات", null, MnuPaymentReport_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تصدير البيانات", null, MnuExportData_Click)
            });

            // Settings menu
            mnuSettings = new ToolStripMenuItem("الإعدادات");
            mnuSettings.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إعدادات المستخدم", null, MnuUserSettings_Click),
                new ToolStripMenuItem("إعدادات النظام", null, MnuSystemSettings_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("النسخ الاحتياطي", null, MnuBackup_Click),
                new ToolStripMenuItem("استعادة النسخة الاحتياطية", null, MnuRestore_Click)
            });

            // Help menu
            mnuHelp = new ToolStripMenuItem("المساعدة");
            mnuHelp.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("دليل المستخدم", null, MnuUserGuide_Click),
                new ToolStripMenuItem("حول البرنامج", null, MnuAbout_Click)
            });

            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                mnuCustomers, mnuTransactions, mnuReports, mnuSettings, mnuHelp
            });

            this.Controls.Add(menuStrip);
            this.MainMenuStrip = menuStrip;
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip
            {
                BackColor = Color.FromArgb(233, 236, 239),
                Font = new Font("Tahoma", 9)
            };

            btnAddCustomer = new ToolStripButton
            {
                Text = "زبون جديد",
                Image = CreateIcon(Color.Green),
                ImageTransparentColor = Color.Magenta
            };
            btnAddCustomer.Click += MnuAddCustomer_Click;

            btnAddTransaction = new ToolStripButton
            {
                Text = "معاملة جديدة",
                Image = CreateIcon(Color.Blue),
                ImageTransparentColor = Color.Magenta
            };
            btnAddTransaction.Click += MnuAddTransaction_Click;

            btnReports = new ToolStripButton
            {
                Text = "التقارير",
                Image = CreateIcon(Color.Orange),
                ImageTransparentColor = Color.Magenta
            };
            btnReports.Click += MnuCustomerReport_Click;

            btnBackup = new ToolStripButton
            {
                Text = "نسخة احتياطية",
                Image = CreateIcon(Color.Purple),
                ImageTransparentColor = Color.Magenta
            };
            btnBackup.Click += MnuBackup_Click;

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                btnAddCustomer,
                new ToolStripSeparator(),
                btnAddTransaction,
                new ToolStripSeparator(),
                btnReports,
                new ToolStripSeparator(),
                btnBackup
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(52, 58, 64),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 9)
            };

            var lblStatus = new ToolStripStatusLabel($"مرحباً {_currentUser.DisplayName}")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblTime = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd HH:mm"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { lblStatus, lblTime });
            this.Controls.Add(statusStrip);

            // Update time every minute
            var timer = new System.Windows.Forms.Timer { Interval = 60000 };
            timer.Tick += (s, e) => lblTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();
        }

        private void CreateSidebar()
        {
            pnlSidebar = new Panel
            {
                Width = 200,
                Dock = DockStyle.Right,
                BackColor = Color.FromArgb(52, 58, 64),
                Padding = new Padding(10)
            };

            // Welcome label
            lblWelcome = new Label
            {
                Text = $"مرحباً\n{_currentUser.DisplayName}",
                ForeColor = Color.White,
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Height = 60,
                Dock = DockStyle.Top
            };

            // Stats label
            lblStats = new Label
            {
                Text = "جاري تحميل الإحصائيات...",
                ForeColor = Color.LightGray,
                Font = new Font("Tahoma", 9),
                TextAlign = ContentAlignment.TopRight,
                Height = 100,
                Dock = DockStyle.Top
            };

            // Create sidebar buttons
            CreateSidebarButtons();

            pnlSidebar.Controls.Add(lblWelcome);
            pnlSidebar.Controls.Add(lblStats);
            this.Controls.Add(pnlSidebar);
        }

        private void CreateSidebarButtons()
        {
            var buttonHeight = 40;
            var buttonMargin = 5;
            var currentY = 170;

            var buttons = new (string text, Color color, EventHandler handler)[]
            {
                ("الزبائن", Color.FromArgb(0, 123, 255), MnuViewCustomers_Click),
                ("المعاملات", Color.FromArgb(40, 167, 69), MnuViewTransactions_Click),
                ("المدفوعات", Color.FromArgb(255, 193, 7), MnuPayments_Click),
                ("التذكيرات", Color.FromArgb(220, 53, 69), MnuReminders_Click),
                ("المستندات", Color.FromArgb(108, 117, 125), MnuDocuments_Click),
                ("التقارير", Color.FromArgb(23, 162, 184), MnuCustomerReport_Click)
            };

            foreach (var (text, color, handler) in buttons)
            {
                var btn = new Button
                {
                    Text = text,
                    BackColor = color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 10),
                    Size = new Size(180, buttonHeight),
                    Location = new Point(10, currentY),
                    UseVisualStyleBackColor = false
                };

                btn.FlatAppearance.BorderSize = 0;
                btn.Click += handler;
                pnlSidebar.Controls.Add(btn);

                currentY += buttonHeight + buttonMargin;
            }
        }

        private void CreateMainPanel()
        {
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Welcome message
            var lblMainWelcome = new Label
            {
                Text = "مرحباً بك في نظام إدارة الزبائن\nمحلات أبو علاء",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 100
            };

            pnlMain.Controls.Add(lblMainWelcome);
            this.Controls.Add(pnlMain);
        }

        private void SetupForm()
        {
            // Set form icon
            try
            {
                this.Icon = SystemIcons.Application;
            }
            catch { }

            // Handle form closing
            this.FormClosing += MainForm_FormClosing;
        }

        private async void LoadDashboardAsync()
        {
            try
            {
                var stats = await _databaseService.GetDashboardStatsAsync(_currentUser.Id);
                
                var statsText = $"إجمالي الزبائن: {stats.GetValueOrDefault("TotalCustomers", 0)}\n" +
                               $"الزبائن النشطون: {stats.GetValueOrDefault("ActiveCustomers", 0)}\n" +
                               $"إجمالي المبيعات: {stats.GetValueOrDefault("TotalSales", 0):N0} د.ع\n" +
                               $"إجمالي المدفوعات: {stats.GetValueOrDefault("TotalPayments", 0):N0} د.ع\n" +
                               $"المبلغ المعلق: {stats.GetValueOrDefault("PendingAmount", 0):N0} د.ع\n" +
                               $"التذكيرات المتأخرة: {stats.GetValueOrDefault("OverdueReminders", 0)}";

                lblStats.Text = statsText;
            }
            catch (Exception ex)
            {
                lblStats.Text = "خطأ في تحميل الإحصائيات";
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private Bitmap CreateIcon(Color color)
        {
            var bitmap = new Bitmap(16, 16);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.FillRectangle(new SolidBrush(color), 0, 0, 16, 16);
            }
            return bitmap;
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        // Event handlers
        private void MnuViewCustomers_Click(object sender, EventArgs e) { ShowCustomersForm(); }
        private void MnuAddCustomer_Click(object sender, EventArgs e) { ShowAddCustomerForm(); }
        private void MnuSearchCustomer_Click(object sender, EventArgs e) { ShowSearchCustomerForm(); }
        private void MnuViewTransactions_Click(object sender, EventArgs e) { ShowTransactionsForm(); }
        private void MnuAddTransaction_Click(object sender, EventArgs e) { ShowAddTransactionForm(); }
        private void MnuPayments_Click(object sender, EventArgs e) { ShowPaymentsForm(); }
        private void MnuReminders_Click(object sender, EventArgs e) { ShowRemindersForm(); }
        private void MnuDocuments_Click(object sender, EventArgs e) { ShowDocumentsForm(); }
        private void MnuCustomerReport_Click(object sender, EventArgs e) { ShowCustomerReportForm(); }
        private void MnuSalesReport_Click(object sender, EventArgs e) { ShowSalesReportForm(); }
        private void MnuPaymentReport_Click(object sender, EventArgs e) { ShowPaymentReportForm(); }
        private void MnuExportData_Click(object sender, EventArgs e) { ShowExportDataForm(); }
        private void MnuUserSettings_Click(object sender, EventArgs e) { ShowUserSettingsForm(); }
        private void MnuSystemSettings_Click(object sender, EventArgs e) { ShowSystemSettingsForm(); }
        private void MnuBackup_Click(object sender, EventArgs e) { ShowBackupForm(); }
        private void MnuRestore_Click(object sender, EventArgs e) { ShowRestoreForm(); }
        private void MnuUserGuide_Click(object sender, EventArgs e) { ShowUserGuideForm(); }
        private void MnuAbout_Click(object sender, EventArgs e) { ShowAboutForm(); }

        // Form methods
        private void ShowCustomersForm()
        {
            var customersForm = new CustomersForm(_databaseService, _currentUser);
            customersForm.ShowDialog();
            // Refresh dashboard after closing customers form
            LoadDashboardAsync();
        }

        private void ShowAddCustomerForm()
        {
            var addForm = new CustomerEditForm(_databaseService, _currentUser);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadDashboardAsync();
            }
        }

        private void ShowSearchCustomerForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowTransactionsForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowAddTransactionForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowPaymentsForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowRemindersForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowDocumentsForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowCustomerReportForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowSalesReportForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowPaymentReportForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowExportDataForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowUserSettingsForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowSystemSettingsForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }

        private void ShowBackupForm()
        {
            var result = MessageBox.Show("هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟",
                "النسخ الاحتياطي", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("سيتم تنفيذ ميزة النسخ الاحتياطي قريباً", "قيد التطوير");
            }
        }

        private void ShowRestoreForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }
        private void ShowUserGuideForm() { MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير"); }

        private void ShowAboutForm()
        {
            var aboutMessage = "نظام إدارة الزبائن - محلات أبو علاء\n" +
                              "الإصدار 3.0.0\n\n" +
                              "تطبيق سطح مكتب محلي\n" +
                              "قاعدة بيانات SQLite محلية\n" +
                              "واجهة عربية كاملة\n\n" +
                              "© 2024 محلات أبو علاء";

            MessageBox.Show(aboutMessage, "حول البرنامج",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
