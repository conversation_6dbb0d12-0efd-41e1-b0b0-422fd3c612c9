نظام إدارة الزبائن - محلات أبو علاء
===================================

🏪 تطبيق سطح مكتب محلي متطور لإدارة الزبائن والمبيعات

الإصدار: 3.0.2 (تطبيق سطح مكتب محلي - مُحدث)
تاريخ البناء: 2025-06-29
التقنية: .NET 6 WinForms + SQLite

🔧 إصلاحات هذا الإصدار:
=======================
✅ إصلاح مشكلة "فشل في تهيئة قاعدة البيانات"
✅ تحسين مسار قاعدة البيانات المحلية
✅ إصلاح مشكلة SplashForm المُتخلص منها
✅ تحسين معالجة الأخطاء
✅ استقرار أفضل في التشغيل

المميزات الرئيسية:
==================
✅ تطبيق سطح مكتب محلي بالكامل (لا يحتاج إنترنت)
✅ قاعدة بيانات SQLite محلية وآمنة
✅ واجهة عربية كاملة مع دعم RTL
✅ إدارة شاملة للزبائن والمعلومات
✅ تتبع المبيعات والمدفوعات
✅ تقارير مالية مفصلة
✅ نظام تذكيرات متقدم
✅ إدارة المستندات والملفات
✅ نسخ احتياطية تلقائية
✅ أمان عالي مع تشفير كلمات المرور
✅ ملف تنفيذي واحد مستقل

طريقة التشغيل:
===============
1️⃣ انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
2️⃣ أو شغل "CRMDesktopApp.exe" مباشرة
3️⃣ استخدم بيانات الدخول الافتراضية:
   👤 اسم المستخدم: admin
   🔑 كلمة المرور: admin123

⚠️ مهم جداً: غيّر كلمة المرور فوراً بعد الدخول الأول!

متطلبات النظام:
================
• نظام التشغيل: Windows 7 أو أحدث (64-bit)
• الذاكرة: 2 جيجابايت RAM كحد أدنى
• مساحة القرص: 300 ميجابايت
• لا يحتاج اتصال بالإنترنت
• لا يحتاج تثبيت .NET Framework

مجلدات البيانات:
=================
📁 قاعدة البيانات: 
   - الأولوية الأولى: نفس مجلد التطبيق\CRM_Database.db
   - الأولوية الثانية: %APPDATA%\AbuAlaaStoresCRM\CRM_Database.db
📁 المستندات: %APPDATA%\AbuAlaaStoresCRM\Documents\
📁 النسخ الاحتياطية: %APPDATA%\AbuAlaaStoresCRM\Backups\
📁 التقارير: %APPDATA%\AbuAlaaStoresCRM\Reports\

استكشاف الأخطاء:
==================
❓ إذا لم يعمل التطبيق:
   • تأكد من تشغيله كمدير
   • تحقق من إعدادات مكافح الفيروسات
   • أعد تشغيل الكمبيوتر

❓ مشاكل في قاعدة البيانات:
   • احذف ملف CRM_Database.db وأعد تشغيل التطبيق
   • سيتم إنشاء قاعدة بيانات جديدة تلقائياً

❓ مشاكل في الأداء:
   • تأكد من وجود مساحة كافية على القرص
   • أغلق البرامج غير الضرورية
   • أعد تشغيل التطبيق

❓ رسالة "فشل في تهيئة قاعدة البيانات":
   • تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
   • شغل التطبيق كمدير
   • تأكد من عدم حجب مكافح الفيروسات للتطبيق

الوظائف المتاحة:
==================
📋 إدارة الزبائن:
   • إضافة وتعديل وحذف الزبائن
   • تصنيف الزبائن (عادي، مميز، VIP)
   • البحث والفلترة المتقدمة
   • تتبع معلومات الاتصال

💰 إدارة المعاملات:
   • تسجيل المبيعات والمشتريات
   • تتبع المدفوعات والمستحقات
   • حساب الأرصدة تلقائياً
   • طرق دفع متعددة

📊 التقارير والإحصائيات:
   • تقارير الزبائن والمبيعات
   • إحصائيات مالية شاملة
   • تصدير إلى Excel و PDF
   • رسوم بيانية تفاعلية

🔔 نظام التذكيرات:
   • تذكيرات المواعيد والمتابعات
   • تنبيهات المستحقات
   • جدولة المهام
   • إشعارات تلقائية

📁 إدارة المستندات:
   • رفع وتنظيم الملفات
   • ربط المستندات بالزبائن
   • أنواع ملفات متعددة
   • بحث في المحتوى

النسخ الاحتياطية:
==================
💾 يُنصح بعمل نسخة احتياطية دورية من:
   • ملف قاعدة البيانات
   • مجلد المستندات
   • إعدادات التطبيق

🔄 النسخ الاحتياطي التلقائي:
   • يتم تلقائياً كل يوم في الساعة 2:00 صباحاً
   • يحتفظ بآخر 30 نسخة احتياطية
   • يمكن تعطيله من إعدادات التطبيق

الأمان والخصوصية:
===================
🔒 الأمان:
   • تشفير كلمات المرور باستخدام BCrypt
   • قاعدة بيانات محلية آمنة
   • لا يتم إرسال أي بيانات عبر الإنترنت
   • حماية من الوصول غير المصرح

🛡️ الخصوصية:
   • جميع البيانات محفوظة محلياً
   • لا يتم جمع أي معلومات شخصية
   • لا توجد اتصالات خارجية
   • تحكم كامل في البيانات

معلومات تقنية:
================
🔧 التقنيات المستخدمة:
   • .NET 6.0 Windows Forms
   • SQLite Database
   • Entity Framework Core
   • BCrypt للتشفير
   • ClosedXML للتقارير

📊 الأداء:
   • بدء تشغيل سريع
   • استهلاك ذاكرة منخفض
   • استجابة فورية
   • معالجة بيانات محسنة

الدعم الفني:
============
📧 للدعم الفني والاستفسارات
📞 متاح خلال ساعات العمل الرسمية
🔧 تحديثات مجانية

© 2025 محلات أبو علاء - جميع الحقوق محفوظة
تطبيق سطح مكتب محلي - لا يحتاج اتصال بالإنترنت
مطور بتقنية .NET 6 WinForms مع قاعدة بيانات SQLite محلية

الإصدار 3.0.2 - مُحدث ومُحسن
