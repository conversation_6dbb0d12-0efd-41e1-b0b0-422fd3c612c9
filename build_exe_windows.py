import os
import subprocess
import sys

def build_exe_windows():
    print("🚀 جاري بناء ملف تنفيذي للويندوز مع نافذة بدء التشغيل...")
    print("هذا قد يستغرق بضع دقائق...")
    
    # إنشاء مجلد البناء
    build_dir = 'build_exe'
    if not os.path.exists(build_dir):
        os.makedirs(build_dir)
    
    # إنشاء مجلد uploads إذا لم يكن موجوداً
    if not os.path.exists('uploads'):
        os.makedirs('uploads')
    
    # حذف الملف القديم إذا كان موجوداً
    old_exe = os.path.join(build_dir, 'محلات_أبو_علاء_CRM.exe')
    if os.path.exists(old_exe):
        try:
            os.remove(old_exe)
            print("🗑️ تم حذف الملف القديم")
        except:
            print("⚠️ لا يمكن حذف الملف القديم، سيتم استبداله")
    
    # أمر PyInstaller للنسخة المحسنة
    cmd = [
        'pyinstaller',
        '--onefile',
        '--noconsole',
        '--name=محلات_أبو_علاء_CRM',
        '--distpath=build_exe',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=uploads;uploads',
        '--hidden-import=tkinter',
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--clean',
        'app_windows.py'
    ]
    
    try:
        print("📦 تشغيل PyInstaller...")
        result = subprocess.run(cmd, shell=True, check=True)
        
        if result.returncode == 0:
            print("\n✅ تم بناء الملف التنفيذي بنجاح!")
            print(f"📁 الملف موجود في: {build_dir}/محلات_أبو_علاء_CRM.exe")
            
            # إنشاء ملف README للمستخدم
            readme_content = """# محلات أبو علاء CRM - ملف تنفيذي للويندوز

## كيفية الاستخدام:

1. **تشغيل التطبيق:**
   - انقر مرتين على ملف "محلات_أبو_علاء_CRM.exe"
   - ستظهر نافذة بدء التشغيل
   - سيتم فتح المتصفح تلقائياً خلال ثانيتين

2. **الوصول للتطبيق:**
   - سيتم فتح المتصفح تلقائياً على: http://127.0.0.1:5000
   - أو: http://localhost:5000

3. **بيانات الدخول:**
   - اسم المستخدم: admin
   - كلمة المرور: admin123

## المميزات الجديدة:
- نافذة بدء التشغيل تظهر معلومات التطبيق
- فتح المتصفح تلقائياً
- واجهة مستخدم محسنة للويندوز

## ملاحظات مهمة:
- تأكد من أن جدار الحماية لا يمنع التطبيق
- إذا لم يعمل، جرب تشغيل الملف كمدير (Run as Administrator)
- قاعدة البيانات ستُنشأ تلقائياً في نفس مجلد الملف التنفيذي

## استكشاف الأخطاء:
- إذا لم يفتح المتصفح تلقائياً، افتحه يدوياً واذهب للرابط أعلاه
- تأكد من عدم تشغيل تطبيق آخر على المنفذ 5000

تم التطوير بواسطة: خالد شجاع
"""
            
            with open(f'{build_dir}/README.txt', 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print("\n📋 تم إنشاء ملف README.txt مع التعليمات")
            print("\n🎉 التطبيق جاهز للاستخدام!")
            print("\n✨ المميزات الجديدة:")
            print("   - نافذة بدء التشغيل")
            print("   - فتح المتصفح تلقائياً")
            print("   - واجهة مستخدم محسنة")
            
        else:
            print(f"\n❌ حدث خطأ أثناء البناء")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"\n❌ حدث خطأ في PyInstaller: {e}")
        return False
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe_windows() 