#!/usr/bin/env python3
"""
إنشاء جميع أنواع ملفات التنصيب لنظام إدارة الزبائن
يدعم: Inno Setup، التنصيب البسيط، النسخة المحمولة
"""

import subprocess
import os
import sys
import shutil
import zipfile
from datetime import datetime

class InstallerCreator:
    def __init__(self):
        self.success_count = 0
        self.total_count = 0
        
    def log_result(self, task_name, success, details=""):
        """تسجيل نتيجة المهمة"""
        self.total_count += 1
        if success:
            self.success_count += 1
            print(f"✅ {task_name}")
            if details:
                print(f"   {details}")
        else:
            print(f"❌ {task_name}")
            if details:
                print(f"   {details}")
    
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        print("🔍 فحص المتطلبات...")
        
        # فحص الملف التنفيذي
        exe_exists = os.path.exists("dist/AbuAlaaStoresCRM.exe")
        self.log_result("الملف التنفيذي موجود", exe_exists, 
                       "dist/AbuAlaaStoresCRM.exe" if exe_exists else "يرجى تشغيل build_exe.py أولاً")
        
        # فحص المجلدات المطلوبة
        required_dirs = ["templates", "static"]
        for dir_name in required_dirs:
            exists = os.path.exists(dir_name)
            self.log_result(f"مجلد {dir_name}", exists)
            if not exists:
                os.makedirs(dir_name, exist_ok=True)
                print(f"   تم إنشاء المجلد: {dir_name}")
        
        # إنشاء مجلد uploads إذا لم يكن موجوداً
        if not os.path.exists("uploads"):
            os.makedirs("uploads", exist_ok=True)
            print("   تم إنشاء مجلد uploads")
        
        return exe_exists
    
    def create_portable_version(self):
        """إنشاء النسخة المحمولة"""
        print("\n📦 إنشاء النسخة المحمولة...")
        
        try:
            portable_dir = "portable_version"
            if os.path.exists(portable_dir):
                shutil.rmtree(portable_dir)
            os.makedirs(portable_dir)
            
            # نسخ الملفات الأساسية
            files_to_copy = [
                ("dist/AbuAlaaStoresCRM.exe", "AbuAlaaStoresCRM.exe"),
                ("README.md", "README.md"),
                ("SETUP_GUIDE.md", "دليل_الاستخدام.md"),
                ("LICENSE.txt", "الترخيص.txt")
            ]
            
            for src, dst in files_to_copy:
                if os.path.exists(src):
                    shutil.copy2(src, os.path.join(portable_dir, dst))
            
            # نسخ المجلدات
            dirs_to_copy = ["templates", "static", "uploads"]
            for dir_name in dirs_to_copy:
                if os.path.exists(dir_name):
                    shutil.copytree(dir_name, os.path.join(portable_dir, dir_name))
            
            # إنشاء ملف التشغيل
            self.create_portable_launcher(portable_dir)
            
            # إنشاء ملف المعلومات
            self.create_portable_info(portable_dir)
            
            self.log_result("النسخة المحمولة", True, f"المجلد: {os.path.abspath(portable_dir)}")
            return True
            
        except Exception as e:
            self.log_result("النسخة المحمولة", False, str(e))
            return False
    
    def create_portable_launcher(self, portable_dir):
        """إنشاء ملف تشغيل للنسخة المحمولة"""
        launcher_script = """@echo off
chcp 65001 >nul
title محلات أبو علاء - نظام إدارة الزبائن
color 0A

echo.
echo ========================================
echo    محلات أبو علاء - نظام إدارة الزبائن
echo           النسخة المحمولة v2.0.0
echo ========================================
echo.
echo 🚀 جاري بدء تشغيل النظام...
echo.
echo 📋 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo ⚠️  يُرجى تغيير كلمة المرور فور الدخول الأول
echo.
echo 🌐 سيتم فتح المتصفح تلقائياً...
echo 🔴 أغلق هذه النافذة لإيقاف البرنامج
echo.

REM تشغيل البرنامج
start "" "AbuAlaaStoresCRM.exe"

REM انتظار إغلاق البرنامج
echo ⏳ البرنامج يعمل الآن...
echo    يمكنك إغلاق هذه النافذة لإيقاف البرنامج
echo.
pause >nul
"""
        
        with open(os.path.join(portable_dir, "تشغيل_البرنامج.bat"), "w", encoding="utf-8") as f:
            f.write(launcher_script)
    
    def create_portable_info(self, portable_dir):
        """إنشاء ملف معلومات للنسخة المحمولة"""
        info_text = f"""النسخة المحمولة - نظام إدارة الزبائن
=====================================

📅 تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M")}
🏪 محلات أبو علاء - نظام إدارة الزبائن المتطور
📦 الإصدار: 2.0.0

مميزات النسخة المحمولة:
========================
✅ لا تحتاج تثبيت
✅ تعمل من أي مجلد
✅ يمكن نسخها على فلاش ميموري
✅ جميع البيانات محفوظة محلياً
✅ سهلة النقل والنسخ الاحتياطي

طريقة الاستخدام:
=================
1️⃣ انقر نقراً مزدوجاً على "تشغيل_البرنامج.bat"
2️⃣ أو شغل "AbuAlaaStoresCRM.exe" مباشرة
3️⃣ سيفتح المتصفح تلقائياً
4️⃣ استخدم بيانات الدخول الافتراضية

بيانات الدخول الافتراضية:
==========================
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123

⚠️ مهم جداً: غيّر كلمة المرور فوراً بعد الدخول الأول!

ملفات النظام:
=============
📄 AbuAlaaStoresCRM.exe - الملف التنفيذي الرئيسي
📁 templates/ - قوالب الواجهة
📁 static/ - الملفات الثابتة (CSS, JS, صور)
📁 uploads/ - مجلد رفع الملفات
📄 crm.db - قاعدة البيانات (سيتم إنشاؤها تلقائياً)

النسخ الاحتياطية:
==================
💾 انسخ المجلد كاملاً لعمل نسخة احتياطية
💾 ملف قاعدة البيانات: crm.db
💾 ملفات المستندات: مجلد uploads/

استكشاف الأخطاء:
==================
❓ إذا لم يعمل البرنامج:
   • تأكد من عدم حجب مكافح الفيروسات للملف
   • شغل البرنامج كمدير
   • تأكد من وجود جميع الملفات

❓ إذا لم يفتح المتصفح:
   • افتح المتصفح يدوياً
   • اذهب إلى: http://127.0.0.1:5000

❓ مشاكل في قاعدة البيانات:
   • احذف ملف crm.db وأعد تشغيل البرنامج
   • سيتم إنشاء قاعدة بيانات جديدة تلقائياً

الدعم الفني:
============
📧 للدعم الفني والاستفسارات
📞 متاح خلال ساعات العمل
🔧 تحديثات مجانية

© 2024 محلات أبو علاء - جميع الحقوق محفوظة
النسخة المحمولة - لا تحتاج تثبيت
"""
        
        with open(os.path.join(portable_dir, "اقرأني_أولاً.txt"), "w", encoding="utf-8") as f:
            f.write(info_text)
    
    def create_simple_installer(self):
        """إنشاء ملف التنصيب البسيط"""
        print("\n🔧 إنشاء ملف التنصيب البسيط...")
        
        try:
            # استيراد من الملف الموجود
            from create_simple_installer import create_simple_installer as create_simple
            success = create_simple()
            self.log_result("التنصيب البسيط", success, "simple_installer/install.bat")
            return success
        except Exception as e:
            self.log_result("التنصيب البسيط", False, str(e))
            return False
    
    def create_inno_installer(self):
        """إنشاء ملف التنصيب باستخدام Inno Setup"""
        print("\n🏗️  إنشاء ملف التنصيب المتقدم (Inno Setup)...")
        
        # البحث عن Inno Setup
        inno_paths = [
            r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
            r"C:\Program Files\Inno Setup 6\ISCC.exe",
            r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
            r"C:\Program Files\Inno Setup 5\ISCC.exe"
        ]
        
        inno_path = None
        for path in inno_paths:
            if os.path.exists(path):
                inno_path = path
                break
        
        if not inno_path:
            self.log_result("Inno Setup", False, "غير مثبت - يمكن تحميله من jrsoftware.org")
            return False
        
        try:
            # إنشاء مجلد المخرجات
            if not os.path.exists("installer_output"):
                os.makedirs("installer_output")
            
            # تشغيل Inno Setup
            result = subprocess.run([inno_path, "installer.iss"], 
                                  capture_output=True, text=True, check=True)
            
            # التحقق من وجود الملف
            installer_file = "installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe"
            if os.path.exists(installer_file):
                file_size = os.path.getsize(installer_file) / (1024 * 1024)
                self.log_result("Inno Setup", True, f"{installer_file} ({file_size:.1f} MB)")
                return True
            else:
                self.log_result("Inno Setup", False, "لم يتم إنشاء الملف")
                return False
                
        except subprocess.CalledProcessError as e:
            self.log_result("Inno Setup", False, f"خطأ في التجميع: {e}")
            return False
    
    def create_distribution_package(self):
        """إنشاء حزمة التوزيع الشاملة"""
        print("\n📦 إنشاء حزمة التوزيع الشاملة...")
        
        try:
            zip_filename = f"AbuAlaaStoresCRM_v2.0.0_Complete_{datetime.now().strftime('%Y%m%d')}.zip"
            
            with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة ملف Inno Setup إذا كان موجوداً
                inno_file = "installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe"
                if os.path.exists(inno_file):
                    zipf.write(inno_file, "1_التنصيب_المتقدم/AbuAlaaStoresCRM_Setup.exe")
                
                # إضافة التنصيب البسيط
                simple_dir = "simple_installer"
                if os.path.exists(simple_dir):
                    for root, dirs, files in os.walk(simple_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.join("2_التنصيب_البسيط", 
                                                  os.path.relpath(file_path, simple_dir))
                            zipf.write(file_path, arc_path)
                
                # إضافة النسخة المحمولة
                portable_dir = "portable_version"
                if os.path.exists(portable_dir):
                    for root, dirs, files in os.walk(portable_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.join("3_النسخة_المحمولة", 
                                                  os.path.relpath(file_path, portable_dir))
                            zipf.write(file_path, arc_path)
                
                # إضافة التوثيق
                docs = ["README.md", "SETUP_GUIDE.md", "LICENSE.txt"]
                for doc in docs:
                    if os.path.exists(doc):
                        zipf.write(doc, f"4_التوثيق/{doc}")
                
                # إضافة ملف تعليمات
                instructions = """حزمة التوزيع الشاملة - نظام إدارة الزبائن
===============================================

هذه الحزمة تحتوي على جميع إصدارات التنصيب:

📁 1_التنصيب_المتقدم/
   ملف تنصيب احترافي مع واجهة رسومية
   يتطلب صلاحيات المدير
   ينشئ اختصارات تلقائياً
   
📁 2_التنصيب_البسيط/
   ملف تنصيب بسيط بدون برامج إضافية
   شغل install.bat كمدير
   
📁 3_النسخة_المحمولة/
   لا تحتاج تثبيت
   شغل تشغيل_البرنامج.bat
   يمكن نسخها على فلاش ميموري
   
📁 4_التوثيق/
   دليل الاستخدام والتوثيق الكامل

اختر الطريقة التي تناسبك!

بيانات الدخول الافتراضية:
اسم المستخدم: admin
كلمة المرور: admin123

⚠️ غيّر كلمة المرور فوراً بعد الدخول!
"""
                
                zipf.writestr("اقرأني_أولاً.txt", instructions.encode('utf-8'))
            
            file_size = os.path.getsize(zip_filename) / (1024 * 1024)
            self.log_result("حزمة التوزيع", True, f"{zip_filename} ({file_size:.1f} MB)")
            return True
            
        except Exception as e:
            self.log_result("حزمة التوزيع", False, str(e))
            return False
    
    def print_summary(self):
        """طباعة ملخص النتائج"""
        print("\n" + "=" * 50)
        print("📊 ملخص النتائج")
        print("=" * 50)
        print(f"✅ نجح: {self.success_count}/{self.total_count}")
        print(f"❌ فشل: {self.total_count - self.success_count}/{self.total_count}")
        
        if self.success_count > 0:
            print(f"\n🎉 تم إنشاء {self.success_count} ملف تنصيب بنجاح!")
            print("\n📋 الملفات المتاحة:")
            
            if os.path.exists("installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe"):
                print("   🏗️  التنصيب المتقدم: installer_output/")
            
            if os.path.exists("simple_installer"):
                print("   🔧 التنصيب البسيط: simple_installer/")
            
            if os.path.exists("portable_version"):
                print("   📦 النسخة المحمولة: portable_version/")
            
            # البحث عن حزمة التوزيع
            for file in os.listdir("."):
                if file.startswith("AbuAlaaStoresCRM_v2.0.0_Complete_") and file.endswith(".zip"):
                    print(f"   📦 حزمة التوزيع: {file}")
                    break
        else:
            print("\n💥 لم يتم إنشاء أي ملف تنصيب!")

def main():
    """الدالة الرئيسية"""
    creator = InstallerCreator()
    
    print("🚀 إنشاء جميع ملفات التنصيب - نظام إدارة الزبائن")
    print("=" * 60)
    
    # فحص المتطلبات
    if not creator.check_prerequisites():
        print("\n❌ المتطلبات الأساسية غير متوفرة!")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء جميع أنواع التنصيب
    creator.create_portable_version()
    creator.create_simple_installer()
    creator.create_inno_installer()
    creator.create_distribution_package()
    
    # طباعة الملخص
    creator.print_summary()
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
