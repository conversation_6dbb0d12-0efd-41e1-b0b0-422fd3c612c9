#!/usr/bin/env python3
"""
Desktop Launcher for Abu Alaa Stores CRM
Provides a better desktop experience with system tray integration
"""

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import webbrowser
import sys
import os
import time
import subprocess
import socket
from PIL import Image, ImageTk
import pystray
from pystray import MenuItem as item

class CRMDesktopLauncher:
    def __init__(self):
        self.root = None
        self.server_process = None
        self.port = self.find_free_port()
        self.url = f'http://127.0.0.1:{self.port}'
        self.tray_icon = None
        self.running = False
        
    def find_free_port(self):
        """Find a free port for the Flask server"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port
    
    def create_splash_screen(self):
        """Create a splash screen while the app loads"""
        self.root = tk.Tk()
        self.root.title("Abu Alaa Stores CRM")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.root.eval('tk::PlaceWindow . center')
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Abu Alaa Stores CRM", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Initializing application...", 
                                     font=('Arial', 10))
        self.status_label.grid(row=1, column=0, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        self.progress.start()
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, pady=(10, 0))
        
        # Open browser button (initially disabled)
        self.open_button = ttk.Button(button_frame, text="Open Application", 
                                     command=self.open_browser, state='disabled')
        self.open_button.grid(row=0, column=0, padx=(0, 10))
        
        # Minimize to tray button
        self.tray_button = ttk.Button(button_frame, text="Minimize to Tray", 
                                     command=self.minimize_to_tray, state='disabled')
        self.tray_button.grid(row=0, column=1, padx=(10, 0))
        
        # Exit button
        exit_button = ttk.Button(button_frame, text="Exit", command=self.exit_app)
        exit_button.grid(row=0, column=2, padx=(10, 0))
        
        # Info text
        info_text = tk.Text(main_frame, height=6, width=50, wrap=tk.WORD)
        info_text.grid(row=4, column=0, pady=(20, 0))
        info_text.insert(tk.END, 
            "Welcome to Abu Alaa Stores CRM Desktop Application!\n\n"
            "• Default login: admin / admin123\n"
            "• The application runs locally on your computer\n"
            "• Your data is stored securely in a local database\n"
            "• You can minimize the app to system tray")
        info_text.config(state='disabled')
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
    def start_server(self):
        """Start the Flask server"""
        def run_server():
            try:
                self.update_status("Starting server...")
                
                # Import and start the Flask app
                sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                from app import app, initialize_database
                
                # Initialize database
                initialize_database()
                
                self.update_status("Server starting...")
                app.run(host='127.0.0.1', port=self.port, debug=False, 
                       use_reloader=False, threaded=True)
                       
            except Exception as e:
                self.update_status(f"Error: {str(e)}")
                messagebox.showerror("Error", f"Failed to start server: {str(e)}")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        self.root.after(3000, self.check_server)
    
    def check_server(self):
        """Check if server is running"""
        try:
            import requests
            response = requests.get(self.url, timeout=5)
            if response.status_code == 200:
                self.update_status("Server ready!")
                self.progress.stop()
                self.open_button.config(state='normal')
                self.tray_button.config(state='normal')
                self.running = True
                return
        except:
            pass
        
        # Try again in 1 second
        self.root.after(1000, self.check_server)
    
    def update_status(self, message):
        """Update status label"""
        if self.status_label:
            self.status_label.config(text=message)
            self.root.update()
    
    def open_browser(self):
        """Open the application in browser"""
        webbrowser.open(self.url)
    
    def minimize_to_tray(self):
        """Minimize application to system tray"""
        self.root.withdraw()
        self.create_tray_icon()
    
    def create_tray_icon(self):
        """Create system tray icon"""
        # Create a simple icon (you can replace with a proper icon file)
        image = Image.new('RGB', (64, 64), color='blue')
        
        menu = pystray.Menu(
            item('Open Application', self.open_browser),
            item('Show Window', self.show_window),
            pystray.Menu.SEPARATOR,
            item('Exit', self.exit_app)
        )
        
        self.tray_icon = pystray.Icon("CRM", image, "Abu Alaa Stores CRM", menu)
        
        # Run tray icon in separate thread
        tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
        tray_thread.start()
    
    def show_window(self):
        """Show the main window"""
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.deiconify()
        self.root.lift()
    
    def exit_app(self):
        """Exit the application"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the CRM application?"):
            if self.tray_icon:
                self.tray_icon.stop()
            if self.root:
                self.root.quit()
            sys.exit(0)
    
    def run(self):
        """Run the desktop launcher"""
        self.create_splash_screen()
        
        # Start server in background
        self.root.after(1000, self.start_server)
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.minimize_to_tray)
        
        # Start GUI
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        launcher = CRMDesktopLauncher()
        launcher.run()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start application: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
