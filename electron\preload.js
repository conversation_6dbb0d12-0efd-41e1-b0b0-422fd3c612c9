const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Menu actions
    onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
    
    // Window controls
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    closeWindow: () => ipcRenderer.invoke('close-window'),
    
    // Application info
    getVersion: () => ipcRenderer.invoke('get-version'),
    
    // File operations
    openFile: () => ipcRenderer.invoke('open-file'),
    saveFile: (data) => ipcRenderer.invoke('save-file', data),
    
    // Notifications
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
    
    // System integration
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    showInFolder: (path) => ipcRenderer.invoke('show-in-folder', path)
});

// Add custom CSS for better desktop integration
document.addEventListener('DOMContentLoaded', () => {
    // Add desktop-specific styles
    const style = document.createElement('style');
    style.textContent = `
        /* Desktop-specific styles */
        body {
            -webkit-user-select: none;
            user-select: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* Make text selectable in input fields */
        input, textarea, [contenteditable] {
            -webkit-user-select: text;
            user-select: text;
        }
        
        /* Custom scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* Desktop app header */
        .desktop-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }
        
        .desktop-header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .desktop-controls {
            display: flex;
            gap: 10px;
        }
        
        .desktop-controls button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .desktop-controls button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    `;
    document.head.appendChild(style);
    
    // Add desktop header if not present
    if (!document.querySelector('.desktop-header')) {
        const header = document.createElement('div');
        header.className = 'desktop-header';
        header.innerHTML = `
            <h1>Abu Alaa Stores CRM</h1>
            <div class="desktop-controls">
                <button onclick="window.electronAPI.minimizeWindow()">Minimize</button>
                <button onclick="window.electronAPI.closeWindow()">Close</button>
            </div>
        `;
        document.body.insertBefore(header, document.body.firstChild);
    }
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (event) => {
    // Ctrl+R or F5 - Reload
    if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
        event.preventDefault();
        location.reload();
    }
    
    // Ctrl+Shift+I - Toggle DevTools (only in development)
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
        // This will be handled by the main process
    }
    
    // Ctrl+N - New Customer
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        // Navigate to add customer page
        const addCustomerLink = document.querySelector('a[href*="add_customer"]');
        if (addCustomerLink) {
            addCustomerLink.click();
        }
    }
});

// Add right-click context menu prevention (optional)
document.addEventListener('contextmenu', (event) => {
    // Allow context menu on input fields
    if (!event.target.matches('input, textarea, [contenteditable]')) {
        event.preventDefault();
    }
});

// Add loading indicator for navigation
let loadingIndicator = null;

function showLoading() {
    if (!loadingIndicator) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 9999;
            animation: loading 2s infinite;
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            @keyframes loading {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(loadingIndicator);
}

function hideLoading() {
    if (loadingIndicator && loadingIndicator.parentNode) {
        loadingIndicator.parentNode.removeChild(loadingIndicator);
    }
}

// Show loading on form submissions and navigation
document.addEventListener('submit', showLoading);
document.addEventListener('click', (event) => {
    if (event.target.matches('a[href]') && !event.target.href.startsWith('javascript:')) {
        showLoading();
    }
});

// Hide loading when page loads
window.addEventListener('load', hideLoading);
document.addEventListener('DOMContentLoaded', hideLoading);
