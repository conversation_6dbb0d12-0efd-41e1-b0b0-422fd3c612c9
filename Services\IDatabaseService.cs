using CRMDesktopApp.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CRMDesktopApp.Services
{
    public interface IDatabaseService
    {
        void InitializeDatabase();
        Task<bool> TestConnectionAsync();
        Task<int> ExecuteNonQueryAsync(string sql, object? parameters = null);
        Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null);
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null);
    }

    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync(int userId);
        Task<Customer?> GetCustomerByIdAsync(int id, int userId);
        Task<Customer?> GetCustomerByPhoneAsync(string phone, int userId);
        Task<int> CreateCustomerAsync(Customer customer);
        Task<bool> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id, int userId);
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm, int userId);
        Task<IEnumerable<Customer>> GetCustomersByCategoryAsync(string category, int userId);
        Task<IEnumerable<Customer>> GetCustomersWithPendingAmountAsync(int userId);
        Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal totalBalance, decimal paidAmount);
    }

    public interface ITransactionService
    {
        Task<IEnumerable<Transaction>> GetTransactionsByCustomerAsync(int customerId);
        Task<IEnumerable<Transaction>> GetTransactionsByUserAsync(int userId);
        Task<IEnumerable<Transaction>> GetRecentTransactionsAsync(int userId, int count = 10);
        Task<Transaction?> GetTransactionByIdAsync(int id);
        Task<int> CreateTransactionAsync(Transaction transaction);
        Task<bool> UpdateTransactionAsync(Transaction transaction);
        Task<bool> DeleteTransactionAsync(int id);
        Task<decimal> GetTotalSalesAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalPaymentsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        
        // Payment methods
        Task<IEnumerable<Payment>> GetPaymentsByCustomerAsync(int customerId);
        Task<int> CreatePaymentAsync(Payment payment);
        Task<bool> UpdatePaymentAsync(Payment payment);
        Task<bool> DeletePaymentAsync(int id);
    }

    public interface IReportService
    {
        Task<byte[]> GenerateCustomerReportAsync(int userId);
        Task<byte[]> GenerateTransactionReportAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<byte[]> GeneratePaymentReportAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<byte[]> GenerateCustomerStatementAsync(int customerId);
        Task<Dictionary<string, object>> GetDashboardStatsAsync(int userId);
        Task<IEnumerable<object>> GetSalesChartDataAsync(int userId, int months = 12);
        Task<IEnumerable<object>> GetCustomerCategoryStatsAsync(int userId);
    }

    public interface IAuthenticationService
    {
        Task<User?> AuthenticateAsync(string username, string password);
        Task<User?> GetUserByIdAsync(int id);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<int> CreateUserAsync(User user, string password);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ValidatePasswordAsync(string password, string hash);
        string HashPassword(string password);
        Task<bool> IsUsernameAvailableAsync(string username);
        Task<bool> IsEmailAvailableAsync(string email);
    }

    public interface ICommunicationService
    {
        Task<IEnumerable<Communication>> GetCommunicationsByCustomerAsync(int customerId);
        Task<int> CreateCommunicationAsync(Communication communication);
        Task<bool> UpdateCommunicationAsync(Communication communication);
        Task<bool> DeleteCommunicationAsync(int id);
    }

    public interface IDocumentService
    {
        Task<IEnumerable<Document>> GetDocumentsByCustomerAsync(int customerId);
        Task<int> CreateDocumentAsync(Document document);
        Task<bool> DeleteDocumentAsync(int id);
        Task<string> GetDocumentPathAsync(int id);
        Task<bool> SaveDocumentFileAsync(string filename, byte[] content);
    }

    public interface IReminderService
    {
        Task<IEnumerable<Reminder>> GetRemindersByCustomerAsync(int customerId);
        Task<IEnumerable<Reminder>> GetUpcomingRemindersAsync(int userId, int days = 7);
        Task<IEnumerable<Reminder>> GetOverdueRemindersAsync(int userId);
        Task<int> CreateReminderAsync(Reminder reminder);
        Task<bool> UpdateReminderAsync(Reminder reminder);
        Task<bool> DeleteReminderAsync(int id);
        Task<bool> MarkReminderCompleteAsync(int id);
    }
}
