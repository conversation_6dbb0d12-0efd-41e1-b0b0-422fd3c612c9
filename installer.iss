[Setup]
AppName=محلات أبو علاء - نظام إدارة الزبائن
AppVersion=2.0.0
AppPublisher=محلات أبو علاء
AppPublisherURL=https://abualaastores.com
AppSupportURL=https://abualaastores.com/support
AppUpdatesURL=https://abualaastores.com/updates
DefaultDirName={autopf}\Abu Alaa Stores CRM
DefaultGroupName=محلات أبو علاء CRM
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=INSTALL_INFO.txt
InfoAfterFile=AFTER_INSTALL.txt
OutputDir=installer_output
OutputBaseFilename=AbuAlaaStoresCRM_Setup_v2.0.0
SetupIconFile=icon.ico
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
ShowLanguageDialog=yes
UninstallDisplayIcon={app}\AbuAlaaStoresCRM.exe

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "إضافة إلى قائمة ابدأ"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce

[Files]
; الملف التنفيذي الرئيسي
Source: "dist\AbuAlaaStoresCRM.exe"; DestDir: "{app}"; Flags: ignoreversion
; مجلد القوالب
Source: "templates\*"; DestDir: "{app}\templates"; Flags: ignoreversion recursesubdirs createallsubdirs
; مجلد الملفات الثابتة
Source: "static\*"; DestDir: "{app}\static"; Flags: ignoreversion recursesubdirs createallsubdirs
; مجلد الرفع
Source: "uploads\*"; DestDir: "{app}\uploads"; Flags: ignoreversion recursesubdirs createallsubdirs; Attribs: hidden
; ملفات التوثيق
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "SETUP_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
; ملفات إضافية
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
; أيقونة التطبيق
Source: "icon.ico"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\محلات أبو علاء CRM"; Filename: "{app}\AbuAlaaStoresCRM.exe"; WorkingDir: "{app}"; IconFilename: "{app}\icon.ico"
Name: "{group}\دليل المستخدم"; Filename: "{app}\SETUP_GUIDE.md"
Name: "{group}\إلغاء التثبيت"; Filename: "{uninstallexe}"
Name: "{autodesktop}\محلات أبو علاء CRM"; Filename: "{app}\AbuAlaaStoresCRM.exe"; WorkingDir: "{app}"; IconFilename: "{app}\icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\محلات أبو علاء CRM"; Filename: "{app}\AbuAlaaStoresCRM.exe"; WorkingDir: "{app}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\AbuAlaaStoresCRM.exe"; Description: "{cm:LaunchProgram,محلات أبو علاء CRM}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}\uploads"
Type: filesandordirs; Name: "{app}\instance"
Type: files; Name: "{app}\*.db"
Type: files; Name: "{app}\*.log"

[Registry]
Root: HKCU; Subkey: "Software\Abu Alaa Stores\CRM"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKCU; Subkey: "Software\Abu Alaa Stores\CRM"; ValueType: string; ValueName: "Version"; ValueData: "2.0.0"

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if IsUpgrade() then
  begin
    if MsgBox('تم العثور على إصدار سابق من التطبيق. هل تريد إزالته قبل التثبيت؟', mbConfirmation, MB_YESNO) = IDYES then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

procedure CurPageChanged(CurPageID: Integer);
begin
  if CurPageID = wpWelcome then
  begin
    WizardForm.NextButton.Caption := 'التالي >';
    WizardForm.CancelButton.Caption := 'إلغاء';
  end;
end;
