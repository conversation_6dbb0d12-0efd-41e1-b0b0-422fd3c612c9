using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using CRMDesktopApp.Models;
using CRMDesktopApp.Services;

namespace CRMDesktopApp.Forms
{
    public partial class CustomersForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly User _currentUser;

        // UI Controls
        private DataGridView dgvCustomers;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private ComboBox cmbCategory;
        private ComboBox cmbStatus;
        private Label lblTotal;
        private Panel pnlTop;
        private Panel pnlBottom;

        public CustomersForm(IDatabaseService databaseService, User currentUser)
        {
            _databaseService = databaseService;
            _currentUser = currentUser;
            
            InitializeComponent();
            SetupForm();
            LoadCustomersAsync();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة الزبائن";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Top panel
            pnlTop = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // Search textbox
            txtSearch = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(200, 25),
                Location = new Point(10, 25),
                PlaceholderText = "البحث عن زبون..."
            };

            // Category filter
            cmbCategory = new ComboBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(120, 25),
                Location = new Point(220, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbCategory.Items.AddRange(new[] { "الكل", "عادي", "مميز", "VIP" });
            cmbCategory.SelectedIndex = 0;

            // Status filter
            cmbStatus = new ComboBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(100, 25),
                Location = new Point(350, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatus.Items.AddRange(new[] { "الكل", "نشط", "غير نشط" });
            cmbStatus.SelectedIndex = 0;

            // Buttons
            btnAdd = CreateButton("إضافة", Color.FromArgb(40, 167, 69), new Point(480, 20));
            btnEdit = CreateButton("تعديل", Color.FromArgb(0, 123, 255), new Point(580, 20));
            btnDelete = CreateButton("حذف", Color.FromArgb(220, 53, 69), new Point(680, 20));
            btnRefresh = CreateButton("تحديث", Color.FromArgb(108, 117, 125), new Point(780, 20));

            // Total label
            lblTotal = new Label
            {
                Text = "إجمالي الزبائن: 0",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                Size = new Size(150, 25),
                Location = new Point(10, 50),
                TextAlign = ContentAlignment.MiddleLeft
            };

            pnlTop.Controls.AddRange(new Control[]
            {
                txtSearch, cmbCategory, cmbStatus, btnAdd, btnEdit, btnDelete, btnRefresh, lblTotal
            });

            // Bottom panel
            pnlBottom = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // DataGridView
            dgvCustomers = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Tahoma", 9),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false
            };

            // Configure DataGridView columns
            SetupDataGridView();

            // Add controls to form
            this.Controls.Add(dgvCustomers);
            this.Controls.Add(pnlTop);
            this.Controls.Add(pnlBottom);

            this.ResumeLayout(false);
        }

        private Button CreateButton(string text, Color backColor, Point location)
        {
            return new Button
            {
                Text = text,
                Font = new Font("Tahoma", 9, FontStyle.Bold),
                Size = new Size(80, 35),
                Location = location,
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
        }

        private void SetupDataGridView()
        {
            // Configure header style
            dgvCustomers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvCustomers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10, FontStyle.Bold);
            dgvCustomers.ColumnHeadersHeight = 35;

            // Configure row style
            dgvCustomers.DefaultCellStyle.BackColor = Color.White;
            dgvCustomers.DefaultCellStyle.ForeColor = Color.FromArgb(52, 58, 64);
            dgvCustomers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvCustomers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvCustomers.RowTemplate.Height = 30;

            // Add columns
            dgvCustomers.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "الرقم", Width = 60, Visible = false },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "اسم الزبون", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Phone", HeaderText = "رقم الهاتف", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "البريد الإلكتروني", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "CompanyName", HeaderText = "اسم الشركة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "الفئة", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "الحالة", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "TotalBalance", HeaderText = "إجمالي الرصيد", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "PendingAmount", HeaderText = "المبلغ المعلق", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "تاريخ الإنشاء", Width = 100 }
            });

            // Format currency columns
            dgvCustomers.Columns["TotalBalance"].DefaultCellStyle.Format = "N0";
            dgvCustomers.Columns["PendingAmount"].DefaultCellStyle.Format = "N0";
            dgvCustomers.Columns["CreatedAt"].DefaultCellStyle.Format = "yyyy/MM/dd";

            // Set text alignment
            dgvCustomers.Columns["TotalBalance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvCustomers.Columns["PendingAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void SetupForm()
        {
            // Event handlers
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            txtSearch.TextChanged += TxtSearch_TextChanged;
            cmbCategory.SelectedIndexChanged += Filter_Changed;
            cmbStatus.SelectedIndexChanged += Filter_Changed;
            dgvCustomers.CellDoubleClick += DgvCustomers_CellDoubleClick;

            // Set button styles
            SetButtonStyle(btnAdd);
            SetButtonStyle(btnEdit);
            SetButtonStyle(btnDelete);
            SetButtonStyle(btnRefresh);
        }

        private void SetButtonStyle(Button button)
        {
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(button.BackColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(button.BackColor, 0.1f);
        }

        private async Task LoadCustomersAsync()
        {
            try
            {
                dgvCustomers.DataSource = null;
                
                var customers = await _databaseService.GetCustomersAsync(_currentUser.Id);
                
                var customerData = customers.Select(c => new
                {
                    c.Id,
                    c.Name,
                    c.Phone,
                    c.Email,
                    c.CompanyName,
                    c.Category,
                    c.Status,
                    c.TotalBalance,
                    c.PendingAmount,
                    c.CreatedAt
                }).ToList();

                dgvCustomers.DataSource = customerData;
                lblTotal.Text = $"إجمالي الزبائن: {customers.Count}";

                // Color rows based on pending amount
                foreach (DataGridViewRow row in dgvCustomers.Rows)
                {
                    var pendingAmount = Convert.ToDecimal(row.Cells["PendingAmount"].Value);
                    if (pendingAmount > 0)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205); // Light yellow
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الزبائن: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FilterCustomers()
        {
            if (dgvCustomers.DataSource == null) return;

            var bindingSource = new BindingSource();
            bindingSource.DataSource = dgvCustomers.DataSource;

            string filter = "";

            // Search filter
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                filter += $"(Name LIKE '%{txtSearch.Text}%' OR Phone LIKE '%{txtSearch.Text}%' OR Email LIKE '%{txtSearch.Text}%')";
            }

            // Category filter
            if (cmbCategory.SelectedIndex > 0)
            {
                if (!string.IsNullOrEmpty(filter)) filter += " AND ";
                filter += $"Category = '{cmbCategory.SelectedItem}'";
            }

            // Status filter
            if (cmbStatus.SelectedIndex > 0)
            {
                if (!string.IsNullOrEmpty(filter)) filter += " AND ";
                filter += $"Status = '{cmbStatus.SelectedItem}'";
            }

            bindingSource.Filter = filter;
            dgvCustomers.DataSource = bindingSource;
        }

        private async void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new CustomerEditForm(_databaseService, _currentUser);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                await LoadCustomersAsync();
            }
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار زبون للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["Id"].Value);
            var customer = await _databaseService.GetCustomerByIdAsync(customerId);

            if (customer != null)
            {
                var editForm = new CustomerEditForm(_databaseService, _currentUser, customer);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadCustomersAsync();
                }
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار زبون للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var customerName = dgvCustomers.SelectedRows[0].Cells["Name"].Value.ToString();
            var result = MessageBox.Show($"هل تريد حذف الزبون '{customerName}'؟\n\nسيتم حذف جميع البيانات المرتبطة بهذا الزبون.", 
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["Id"].Value);
                var success = await _databaseService.DeleteCustomerAsync(customerId);

                if (success)
                {
                    MessageBox.Show("تم حذف الزبون بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadCustomersAsync();
                }
                else
                {
                    MessageBox.Show("فشل في حذف الزبون", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadCustomersAsync();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterCustomers();
        }

        private void Filter_Changed(object sender, EventArgs e)
        {
            FilterCustomers();
        }

        private async void DgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, EventArgs.Empty);
            }
        }
    }

    // Placeholder for CustomerEditForm
    public class CustomerEditForm : Form
    {
        public CustomerEditForm(IDatabaseService databaseService, User currentUser, Customer? customer = null)
        {
            this.Text = customer == null ? "إضافة زبون جديد" : "تعديل الزبون";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;

            var lblMessage = new Label
            {
                Text = "سيتم تنفيذ نموذج إضافة/تعديل الزبون قريباً",
                Font = new Font("Tahoma", 12),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            this.Controls.Add(lblMessage);
        }
    }
}
