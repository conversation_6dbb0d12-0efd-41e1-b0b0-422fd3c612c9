#!/usr/bin/env python3
"""
إنشاء ملف التنصيب لنظام إدارة الزبائن - محلات أبو علاء
يقوم بإنشاء ملف setup.exe باستخدام Inno Setup
"""

import subprocess
import os
import sys
import shutil
from pathlib import Path

def check_inno_setup():
    """فحص وجود Inno Setup"""
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in inno_paths:
        if os.path.exists(path):
            print(f"✅ تم العثور على Inno Setup: {path}")
            return path
    
    print("❌ لم يتم العثور على Inno Setup")
    print("يرجى تحميل وتثبيت Inno Setup من:")
    print("https://jrsoftware.org/isdl.php")
    return None

def prepare_files():
    """تحضير الملفات للتنصيب"""
    print("📁 تحضير الملفات...")
    
    # التأكد من وجود الملف التنفيذي
    exe_path = "dist/AbuAlaaStoresCRM.exe"
    if not os.path.exists(exe_path):
        print("❌ الملف التنفيذي غير موجود!")
        print("يرجى تشغيل: python build_exe.py أولاً")
        return False
    
    # التأكد من وجود المجلدات المطلوبة
    required_dirs = ["templates", "static", "uploads"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"⚠️  إنشاء مجلد مفقود: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
    
    # إنشاء أيقونة افتراضية إذا لم تكن موجودة
    if not os.path.exists("icon.ico"):
        print("⚠️  لم يتم العثور على icon.ico، سيتم استخدام الأيقونة الافتراضية")
        # يمكنك إضافة كود لإنشاء أيقونة افتراضية هنا
    
    print("✅ تم تحضير الملفات بنجاح")
    return True

def create_installer_directory():
    """إنشاء مجلد المخرجات"""
    installer_dir = "installer_output"
    if os.path.exists(installer_dir):
        shutil.rmtree(installer_dir)
    os.makedirs(installer_dir)
    print(f"📁 تم إنشاء مجلد المخرجات: {installer_dir}")

def build_installer(inno_path):
    """بناء ملف التنصيب"""
    print("🔨 بناء ملف التنصيب...")
    
    try:
        # تشغيل Inno Setup Compiler
        result = subprocess.run([inno_path, "installer.iss"], 
                              capture_output=True, text=True, check=True)
        
        print("✅ تم إنشاء ملف التنصيب بنجاح!")
        
        # البحث عن ملف التنصيب
        installer_file = "installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe"
        if os.path.exists(installer_file):
            file_size = os.path.getsize(installer_file) / (1024 * 1024)  # بالميجابايت
            print(f"📦 ملف التنصيب: {os.path.abspath(installer_file)}")
            print(f"📏 حجم الملف: {file_size:.1f} ميجابايت")
            return True
        else:
            print("❌ لم يتم العثور على ملف التنصيب")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء ملف التنصيب: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False

def create_portable_version():
    """إنشاء نسخة محمولة"""
    print("\n📦 إنشاء النسخة المحمولة...")
    
    portable_dir = "portable_version"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        ("dist/AbuAlaaStoresCRM.exe", "AbuAlaaStoresCRM.exe"),
        ("README.md", "README.md"),
        ("SETUP_GUIDE.md", "دليل_التثبيت.md"),
        ("LICENSE.txt", "الترخيص.txt")
    ]
    
    dirs_to_copy = ["templates", "static", "uploads"]
    
    try:
        # نسخ الملفات
        for src, dst in files_to_copy:
            if os.path.exists(src):
                shutil.copy2(src, os.path.join(portable_dir, dst))
        
        # نسخ المجلدات
        for dir_name in dirs_to_copy:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, os.path.join(portable_dir, dir_name))
        
        # إنشاء ملف تشغيل
        start_script = """@echo off
title محلات أبو علاء - نظام إدارة الزبائن
echo بدء تشغيل نظام إدارة الزبائن...
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo سيتم فتح المتصفح تلقائياً...
echo أغلق هذه النافذة لإيقاف البرنامج
echo.
AbuAlaaStoresCRM.exe
pause
"""
        
        with open(os.path.join(portable_dir, "تشغيل_البرنامج.bat"), "w", encoding="utf-8") as f:
            f.write(start_script)
        
        # إنشاء ملف معلومات
        info_text = """النسخة المحمولة - نظام إدارة الزبائن
=====================================

هذه نسخة محمولة من نظام إدارة الزبائن لمحلات أبو علاء
لا تحتاج إلى تثبيت، يمكن تشغيلها مباشرة

طريقة التشغيل:
--------------
1. انقر نقراً مزدوجاً على "تشغيل_البرنامج.bat"
2. أو شغل "AbuAlaaStoresCRM.exe" مباشرة

بيانات الدخول:
--------------
اسم المستخدم: admin
كلمة المرور: admin123

ملاحظات:
---------
• جميع البيانات محفوظة في نفس المجلد
• يمكن نسخ المجلد كاملاً لعمل نسخة احتياطية
• لا تحذف أي ملفات من المجلد

للدعم الفني تواصل مع فريق التطوير
"""
        
        with open(os.path.join(portable_dir, "اقرأني.txt"), "w", encoding="utf-8") as f:
            f.write(info_text)
        
        print(f"✅ تم إنشاء النسخة المحمولة: {os.path.abspath(portable_dir)}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة المحمولة: {e}")
        return False

def create_zip_package():
    """إنشاء ملف مضغوط للتوزيع"""
    print("\n📦 إنشاء الملف المضغوط...")
    
    try:
        import zipfile
        
        zip_filename = "AbuAlaaStoresCRM_v2.0.0_Complete.zip"
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة ملف التنصيب
            installer_file = "installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe"
            if os.path.exists(installer_file):
                zipf.write(installer_file, "التنصيب/AbuAlaaStoresCRM_Setup.exe")
            
            # إضافة النسخة المحمولة
            portable_dir = "portable_version"
            if os.path.exists(portable_dir):
                for root, dirs, files in os.walk(portable_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.join("النسخة_المحمولة", 
                                              os.path.relpath(file_path, portable_dir))
                        zipf.write(file_path, arc_path)
            
            # إضافة ملفات التوثيق
            docs = ["README.md", "SETUP_GUIDE.md", "LICENSE.txt"]
            for doc in docs:
                if os.path.exists(doc):
                    zipf.write(doc, f"التوثيق/{doc}")
        
        file_size = os.path.getsize(zip_filename) / (1024 * 1024)
        print(f"✅ تم إنشاء الملف المضغوط: {os.path.abspath(zip_filename)}")
        print(f"📏 حجم الملف: {file_size:.1f} ميجابايت")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الملف المضغوط: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملف التنصيب - نظام إدارة الزبائن")
    print("=" * 50)
    
    # فحص المتطلبات
    inno_path = check_inno_setup()
    if not inno_path:
        print("\n💡 يمكنك أيضاً إنشاء النسخة المحمولة بدون Inno Setup")
        choice = input("هل تريد إنشاء النسخة المحمولة فقط؟ (y/n): ")
        if choice.lower() in ['y', 'yes', 'نعم']:
            if prepare_files():
                create_portable_version()
                create_zip_package()
        return
    
    # تحضير الملفات
    if not prepare_files():
        return
    
    # إنشاء مجلد المخرجات
    create_installer_directory()
    
    # بناء ملف التنصيب
    installer_success = build_installer(inno_path)
    
    # إنشاء النسخة المحمولة
    portable_success = create_portable_version()
    
    # إنشاء الملف المضغوط
    zip_success = create_zip_package()
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج")
    print("=" * 50)
    
    if installer_success:
        print("✅ ملف التنصيب: installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe")
    else:
        print("❌ فشل في إنشاء ملف التنصيب")
    
    if portable_success:
        print("✅ النسخة المحمولة: portable_version/")
    else:
        print("❌ فشل في إنشاء النسخة المحمولة")
    
    if zip_success:
        print("✅ الملف المضغوط: AbuAlaaStoresCRM_v2.0.0_Complete.zip")
    else:
        print("❌ فشل في إنشاء الملف المضغوط")
    
    if installer_success or portable_success:
        print("\n🎉 تم إنشاء ملفات التوزيع بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. اختبر ملف التنصيب على جهاز نظيف")
        print("2. اختبر النسخة المحمولة")
        print("3. وزع الملفات على المستخدمين")
    else:
        print("\n💥 فشل في إنشاء ملفات التوزيع")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
