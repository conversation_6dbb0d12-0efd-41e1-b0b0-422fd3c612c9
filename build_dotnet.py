#!/usr/bin/env python3
"""
Build script for Abu Alaa Stores CRM .NET Desktop Application
Creates a native Windows application using .NET 6
"""

import subprocess
import os
import sys
import shutil
import json

def check_dotnet():
    """Check if .NET SDK is installed"""
    try:
        result = subprocess.run(['dotnet', '--version'], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print(f"✅ .NET SDK {version} found")
        
        # Check if it's .NET 6 or later
        major_version = int(version.split('.')[0])
        if major_version >= 6:
            print(f"✅ .NET version is compatible")
            return True
        else:
            print(f"⚠️  .NET 6 or later is recommended (found {version})")
            return True
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
        print("❌ .NET SDK not found or not accessible")
        print("Please install .NET 6 SDK from https://dotnet.microsoft.com/download")
        return False

def restore_packages():
    """Restore NuGet packages"""
    print("\n📦 Restoring NuGet packages...")
    try:
        subprocess.run(['dotnet', 'restore'], check=True)
        print("✅ Packages restored successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to restore packages: {e}")
        return False

def build_debug():
    """Build debug version"""
    print("\n🔧 Building debug version...")
    try:
        subprocess.run(['dotnet', 'build', '--configuration', 'Debug'], check=True)
        print("✅ Debug build completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Debug build failed: {e}")
        return False

def build_release():
    """Build release version"""
    print("\n🏗️  Building release version...")
    try:
        subprocess.run(['dotnet', 'build', '--configuration', 'Release'], check=True)
        print("✅ Release build completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Release build failed: {e}")
        return False

def publish_self_contained():
    """Publish self-contained executable"""
    print("\n📦 Publishing self-contained executable...")
    
    output_dir = 'publish'
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    try:
        subprocess.run([
            'dotnet', 'publish',
            '--configuration', 'Release',
            '--runtime', 'win-x64',
            '--self-contained', 'true',
            '--output', output_dir,
            '/p:PublishSingleFile=true',
            '/p:PublishReadyToRun=true',
            '/p:IncludeNativeLibrariesForSelfExtract=true'
        ], check=True)
        
        print("✅ Self-contained executable published successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Publishing failed: {e}")
        return False

def publish_framework_dependent():
    """Publish framework-dependent version"""
    print("\n📦 Publishing framework-dependent version...")
    
    output_dir = 'publish_framework'
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    try:
        subprocess.run([
            'dotnet', 'publish',
            '--configuration', 'Release',
            '--output', output_dir
        ], check=True)
        
        print("✅ Framework-dependent version published successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Publishing failed: {e}")
        return False

def create_installer_script():
    """Create installer script using Inno Setup"""
    installer_script = '''
[Setup]
AppName=Abu Alaa Stores CRM
AppVersion=2.0.0
AppPublisher=Abu Alaa Stores
AppPublisherURL=https://abualaastores.com
DefaultDirName={autopf}\\Abu Alaa Stores CRM
DefaultGroupName=Abu Alaa Stores CRM
OutputDir=installer
OutputBaseFilename=AbuAlaaStoresCRM_Setup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "publish\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Abu Alaa Stores CRM"; Filename: "{app}\\CRMDesktopApp.exe"
Name: "{group}\\{cm:UninstallProgram,Abu Alaa Stores CRM}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Abu Alaa Stores CRM"; Filename: "{app}\\CRMDesktopApp.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\\CRMDesktopApp.exe"; Description: "{cm:LaunchProgram,Abu Alaa Stores CRM}"; Flags: nowait postinstall skipifsilent

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;
'''
    
    with open('installer.iss', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("📝 Created Inno Setup installer script: installer.iss")
    print("   To create installer, install Inno Setup and compile the script")

def create_batch_files():
    """Create batch files for easy launching"""
    
    # Debug launcher
    debug_batch = '''@echo off
title Abu Alaa Stores CRM - Debug
echo Starting Abu Alaa Stores CRM (Debug Mode)...
echo.
cd /d "%~dp0"
if exist "bin\\Debug\\net6.0-windows\\CRMDesktopApp.exe" (
    "bin\\Debug\\net6.0-windows\\CRMDesktopApp.exe"
) else (
    echo Debug executable not found. Please build the project first.
    echo Run: dotnet build --configuration Debug
    pause
)
'''
    
    # Release launcher
    release_batch = '''@echo off
title Abu Alaa Stores CRM
echo Starting Abu Alaa Stores CRM...
echo.
cd /d "%~dp0"
if exist "publish\\CRMDesktopApp.exe" (
    "publish\\CRMDesktopApp.exe"
) else if exist "bin\\Release\\net6.0-windows\\CRMDesktopApp.exe" (
    "bin\\Release\\net6.0-windows\\CRMDesktopApp.exe"
) else (
    echo Executable not found. Please build the project first.
    echo Run: dotnet build --configuration Release
    pause
)
'''
    
    with open('Start_Debug.bat', 'w') as f:
        f.write(debug_batch)
    
    with open('Start_Release.bat', 'w') as f:
        f.write(release_batch)
    
    print("📝 Created batch launcher files")

def create_appsettings():
    """Create appsettings.json if it doesn't exist"""
    if not os.path.exists('appsettings.json'):
        settings = {
            "ConnectionStrings": {
                "DefaultConnection": "Data Source=crm.db"
            },
            "Logging": {
                "LogLevel": {
                    "Default": "Information",
                    "Microsoft": "Warning",
                    "Microsoft.Hosting.Lifetime": "Information"
                }
            },
            "Application": {
                "Name": "Abu Alaa Stores CRM",
                "Version": "2.0.0",
                "DefaultLanguage": "en",
                "SupportedLanguages": ["en", "ar"]
            },
            "Security": {
                "PasswordMinLength": 6,
                "RequireDigit": False,
                "RequireUppercase": False,
                "RequireSpecialChar": False
            }
        }
        
        with open('appsettings.json', 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2)
        
        print("📝 Created appsettings.json")

def main():
    """Main build function"""
    print("🚀 Abu Alaa Stores CRM - .NET Build Script")
    print("=" * 60)
    
    # Check prerequisites
    if not check_dotnet():
        input("Press Enter to exit...")
        return False
    
    # Create configuration files
    create_appsettings()
    
    # Restore packages
    if not restore_packages():
        input("Press Enter to exit...")
        return False
    
    # Ask user what to build
    print("\n🤔 What would you like to build?")
    print("1. Debug build (for development)")
    print("2. Release build (optimized)")
    print("3. Self-contained executable (includes .NET runtime)")
    print("4. Framework-dependent (requires .NET runtime)")
    print("5. All versions")
    
    choice = input("Enter your choice (1-5): ").strip()
    
    success = False
    
    if choice == '1':
        success = build_debug()
    elif choice == '2':
        success = build_release()
    elif choice == '3':
        success = build_release() and publish_self_contained()
    elif choice == '4':
        success = build_release() and publish_framework_dependent()
    elif choice == '5':
        print("\n🏗️  Building all versions...")
        debug_ok = build_debug()
        release_ok = build_release()
        self_contained_ok = publish_self_contained()
        framework_ok = publish_framework_dependent()
        success = debug_ok or release_ok or self_contained_ok or framework_ok
    else:
        print("❌ Invalid choice")
        input("Press Enter to exit...")
        return False
    
    # Create additional files
    create_batch_files()
    create_installer_script()
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("🎉 Build completed successfully!")
        print("\n📁 Output locations:")
        if os.path.exists('bin'):
            print(f"  - Debug build: {os.path.abspath('bin/Debug/net6.0-windows')}")
            print(f"  - Release build: {os.path.abspath('bin/Release/net6.0-windows')}")
        if os.path.exists('publish'):
            print(f"  - Self-contained: {os.path.abspath('publish')}")
        if os.path.exists('publish_framework'):
            print(f"  - Framework-dependent: {os.path.abspath('publish_framework')}")
        
        print("\n📋 Next Steps:")
        print("1. Test the application on target systems")
        print("2. Create application icon (icon.ico)")
        print("3. Install Inno Setup to create installer")
        print("4. Compile installer.iss to create setup.exe")
        print("5. Test installer on clean systems")
        
    else:
        print("💥 Build failed!")
        print("\n🔧 Troubleshooting:")
        print("1. Check that .NET 6 SDK is installed")
        print("2. Ensure all NuGet packages are available")
        print("3. Check the console output for specific errors")
        print("4. Try building individual components")
    
    input("\nPress Enter to exit...")
    return success

if __name__ == "__main__":
    main()
