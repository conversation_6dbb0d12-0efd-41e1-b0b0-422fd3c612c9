from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import json
import datetime
from datetime import datetime, timedelta
import uuid
from functools import wraps
import bcrypt
import jwt
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
import openpyxl
from openpyxl.styles import Font, PatternFill
import qrcode
from PIL import Image
import io

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///crm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')
    pin = db.Column(db.String(6))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120))
    national_id = db.Column(db.String(20))
    address = db.Column(db.Text)
    company_name = db.Column(db.String(100))
    category = db.Column(db.String(50), default='عادي')
    status = db.Column(db.String(20), default='نشط')
    total_balance = db.Column(db.Float, default=0.0)
    paid_amount = db.Column(db.Float, default=0.0)
    pending_amount = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    custom_fields = db.Column(db.Text)  # JSON string for custom fields
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'بيع' or 'تسديد'
    amount = db.Column(db.Float, nullable=False)
    transaction_date = db.Column(db.DateTime, default=datetime.utcnow)
    payment_method = db.Column(db.String(50))
    currency = db.Column(db.String(3), default='IQD')
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(50))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    payment_method = db.Column(db.String(50))
    currency = db.Column(db.String(3), default='IQD')
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(50))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Communication(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    type = db.Column(db.String(20))  # call, message, email, meeting
    content = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Reminder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='معلق')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('مطلوب صلاحيات المدير', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            return jsonify({'success': True, 'redirect': url_for('dashboard')})
        else:
            return jsonify({'success': False, 'message': 'بيانات الدخول غير صحيحة'})
    
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        
        if User.query.filter_by(username=username).first():
            return jsonify({'success': False, 'message': 'اسم المستخدم موجود مسبقاً'})
        
        if User.query.filter_by(email=email).first():
            return jsonify({'success': False, 'message': 'البريد الإلكتروني موجود مسبقاً'})
        
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            role='user'
        )
        db.session.add(user)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم التسجيل بنجاح'})
    
    return render_template('register.html')

@app.route('/dashboard')
@login_required
def dashboard():
    # Get dashboard statistics
    total_customers = Customer.query.filter_by(user_id=current_user.id).count()
    active_customers = Customer.query.filter_by(user_id=current_user.id, status='نشط').count()
    total_pending = db.session.query(db.func.sum(Customer.pending_amount)).filter_by(user_id=current_user.id).scalar() or 0
    recent_transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(Transaction.transaction_date.desc()).limit(5).all()
    
    # Get all customers for transaction modal
    all_customers = Customer.query.filter_by(user_id=current_user.id).all()
    
    # Get customers with pending payments
    customers_with_pending = Customer.query.filter_by(user_id=current_user.id).filter(Customer.pending_amount > 0).limit(10).all()
    
    return render_template('dashboard.html', 
                         total_customers=total_customers,
                         active_customers=active_customers,
                         total_pending=total_pending,
                         recent_transactions=recent_transactions,
                         all_customers=all_customers,
                         customers_with_pending=customers_with_pending)

@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    status = request.args.get('status', '')
    
    query = Customer.query.filter_by(user_id=current_user.id)
    
    if search:
        query = query.filter(
            db.or_(
                Customer.name.contains(search),
                Customer.phone.contains(search),
                Customer.email.contains(search)
            )
        )
    
    if category:
        query = query.filter_by(category=category)
    
    if status:
        query = query.filter_by(status=status)
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    categories = db.session.query(Customer.category).distinct().all()
    
    return render_template('customers.html', 
                         customers=customers,
                         categories=categories,
                         search=search,
                         selected_category=category,
                         selected_status=status)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        data = request.get_json()
        
        # Check for duplicate phone number
        existing_customer = Customer.query.filter_by(phone=data['phone'], user_id=current_user.id).first()
        if existing_customer:
            return jsonify({'success': False, 'message': 'الزبون بهذا الرقم موجود مسبقاً'})
        
        customer = Customer(
            name=data['name'],
            phone=data['phone'],
            email=data.get('email', ''),
            national_id=data.get('national_id', ''),
            address=data.get('address', ''),
            company_name=data.get('company_name', ''),
            category=data.get('category', 'عادي'),
            custom_fields=json.dumps(data.get('custom_fields', {})),
            notes=data.get('notes', ''),
            user_id=current_user.id
        )
        
        db.session.add(customer)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم إضافة الزبون بنجاح'})
    
    return render_template('add_customer.html')

@app.route('/customers/<int:customer_id>')
@login_required
def customer_detail(customer_id):
    customer = Customer.query.filter_by(id=customer_id, user_id=current_user.id).first_or_404()
    transactions = Transaction.query.filter_by(customer_id=customer_id).order_by(Transaction.transaction_date.desc()).all()
    payments = Payment.query.filter_by(customer_id=customer_id).order_by(Payment.payment_date.desc()).all()
    communications = Communication.query.filter_by(customer_id=customer_id).order_by(Communication.timestamp.desc()).all()
    documents = Document.query.filter_by(customer_id=customer_id).order_by(Document.upload_date.desc()).all()
    reminders = Reminder.query.filter_by(customer_id=customer_id).order_by(Reminder.due_date).all()
    
    return render_template('customer_detail.html',
                         customer=customer,
                         transactions=transactions,
                         payments=payments,
                         communications=communications,
                         documents=documents,
                         reminders=reminders)

@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    customer = Customer.query.filter_by(id=customer_id, user_id=current_user.id).first_or_404()
    
    if request.method == 'POST':
        data = request.get_json()
        
        # Check for duplicate phone number (excluding current customer)
        existing_customer = Customer.query.filter(
            Customer.phone == data['phone'],
            Customer.id != customer_id,
            Customer.user_id == current_user.id
        ).first()
        
        if existing_customer:
            return jsonify({'success': False, 'message': 'الزبون بهذا الرقم موجود مسبقاً'})
        
        customer.name = data['name']
        customer.phone = data['phone']
        customer.email = data.get('email', '')
        customer.national_id = data.get('national_id', '')
        customer.address = data.get('address', '')
        customer.company_name = data.get('company_name', '')
        customer.category = data.get('category', 'عادي')
        customer.status = data.get('status', 'نشط')
        customer.custom_fields = json.dumps(data.get('custom_fields', {}))
        customer.notes = data.get('notes', '')
        customer.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم تحديث الزبون بنجاح'})
    
    return render_template('edit_customer.html', customer=customer)

@app.route('/customers/<int:customer_id>/delete', methods=['POST'])
@login_required
def delete_customer(customer_id):
    customer = Customer.query.filter_by(id=customer_id, user_id=current_user.id).first_or_404()
    
    # Delete related records
    Transaction.query.filter_by(customer_id=customer_id).delete()
    Payment.query.filter_by(customer_id=customer_id).delete()
    Communication.query.filter_by(customer_id=customer_id).delete()
    Document.query.filter_by(customer_id=customer_id).delete()
    Reminder.query.filter_by(customer_id=customer_id).delete()
    
    db.session.delete(customer)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم حذف الزبون بنجاح'})

@app.route('/transactions/add', methods=['POST'])
@login_required
def add_transaction():
    data = request.get_json()
    
    transaction = Transaction(
        customer_id=data['customer_id'],
        transaction_type=data['transaction_type'],  # 'بيع' or 'تسديد'
        amount=float(data['amount']),
        payment_method=data.get('payment_method', 'نقداً'),
        currency=data.get('currency', 'IQD'),
        notes=data.get('notes', ''),
        receipt_number=data.get('receipt_number', ''),
        user_id=current_user.id
    )
    
    # Update customer balance based on transaction type
    customer = Customer.query.get(data['customer_id'])
    if customer:
        if data['transaction_type'] == 'بيع':
            customer.total_balance += float(data['amount'])
        else:  # تسديد
            customer.paid_amount += float(data['amount'])
        
        customer.pending_amount = max(0, customer.total_balance - customer.paid_amount)
    
    db.session.add(transaction)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم إضافة المعاملة بنجاح'})

@app.route('/payments/add', methods=['POST'])
@login_required
def add_payment():
    data = request.get_json()
    
    payment = Payment(
        customer_id=data['customer_id'],
        amount=float(data['amount']),
        payment_method=data.get('payment_method', 'نقداً'),
        currency=data.get('currency', 'IQD'),
        notes=data.get('notes', ''),
        receipt_number=data.get('receipt_number', ''),
        user_id=current_user.id
    )
    
    # Update customer balance
    customer = Customer.query.get(data['customer_id'])
    if customer:
        customer.paid_amount += float(data['amount'])
        customer.pending_amount = max(0, customer.total_balance - customer.paid_amount)
    
    db.session.add(payment)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم إضافة الدفع بنجاح'})

@app.route('/communications/add', methods=['POST'])
@login_required
def add_communication():
    data = request.get_json()
    
    communication = Communication(
        customer_id=data['customer_id'],
        type=data['type'],
        content=data['content'],
        user_id=current_user.id
    )
    
    db.session.add(communication)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم إضافة سجل التواصل بنجاح'})

@app.route('/documents/upload', methods=['POST'])
@login_required
def upload_document():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    file = request.files['file']
    customer_id = request.form.get('customer_id')
    
    if file.filename == '':
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    if file and file.filename:
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        document = Document(
            customer_id=customer_id,
            filename=unique_filename,
            original_filename=filename,
            file_type=file.content_type,
            user_id=current_user.id
        )
        
        db.session.add(document)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم رفع المستند بنجاح'})

@app.route('/reports')
@login_required
def reports():
    return render_template('reports.html')

@app.route('/reports/customers')
@login_required
def customer_report():
    customers = Customer.query.filter_by(user_id=current_user.id).all()
    
    # Create Excel report
    wb = openpyxl.Workbook()
    ws = wb.active
    if ws:
        ws.title = "تقرير الزبائن"
        
        # Headers
        headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'الفئة', 'الحالة', 'إجمالي الرصيد', 'المدفوع', 'المعلق', 'تاريخ الإنشاء']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Data
        for row, customer in enumerate(customers, 2):
            ws.cell(row=row, column=1, value=customer.name)
            ws.cell(row=row, column=2, value=customer.phone)
            ws.cell(row=row, column=3, value=customer.email)
            ws.cell(row=row, column=4, value=customer.category)
            ws.cell(row=row, column=5, value=customer.status)
            ws.cell(row=row, column=6, value=customer.total_balance)
            ws.cell(row=row, column=7, value=customer.paid_amount)
            ws.cell(row=row, column=8, value=customer.pending_amount)
            ws.cell(row=row, column=9, value=customer.created_at.strftime('%Y-%m-%d'))
    
    # Save to bytes
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'تقرير_الزبائن_{datetime.now().strftime("%Y%m%d")}.xlsx'
    )

@app.route('/settings')
@login_required
def settings():
    return render_template('settings.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # Create admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
    
    app.run(debug=True, host='0.0.0.0', port=5000) 