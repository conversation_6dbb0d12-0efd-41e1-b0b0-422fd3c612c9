from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import json
import datetime
from datetime import datetime, timedelta
import uuid
from functools import wraps
import webbrowser
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///crm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    category = db.Column(db.String(50), default='عادي')
    status = db.Column(db.String(20), default='نشط')
    total_balance = db.Column(db.Float, default=0.0)
    paid_amount = db.Column(db.Float, default=0.0)
    pending_amount = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'بيع' or 'تسديد'
    amount = db.Column(db.Float, nullable=False)
    transaction_date = db.Column(db.DateTime, default=datetime.utcnow)
    payment_method = db.Column(db.String(50))
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return jsonify({'success': True, 'redirect': url_for('dashboard')})
        else:
            return jsonify({'success': False, 'message': 'بيانات الدخول غير صحيحة'})
    
    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    total_customers = Customer.query.filter_by(user_id=current_user.id).count()
    active_customers = Customer.query.filter_by(user_id=current_user.id, status='نشط').count()
    total_pending = db.session.query(db.func.sum(Customer.pending_amount)).filter_by(user_id=current_user.id).scalar() or 0
    recent_transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(Transaction.transaction_date.desc()).limit(5).all()
    all_customers = Customer.query.filter_by(user_id=current_user.id).all()
    customers_with_pending = Customer.query.filter_by(user_id=current_user.id).filter(Customer.pending_amount > 0).limit(10).all()
    
    return render_template('dashboard.html', 
                         total_customers=total_customers,
                         active_customers=active_customers,
                         total_pending=total_pending,
                         recent_transactions=recent_transactions,
                         all_customers=all_customers,
                         customers_with_pending=customers_with_pending)

@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    status = request.args.get('status', '')
    
    query = Customer.query.filter_by(user_id=current_user.id)
    
    if search:
        query = query.filter(
            db.or_(
                Customer.name.contains(search),
                Customer.phone.contains(search),
                Customer.email.contains(search)
            )
        )
    
    if category:
        query = query.filter_by(category=category)
    
    if status:
        query = query.filter_by(status=status)
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    categories = db.session.query(Customer.category).distinct().all()
    
    return render_template('customers.html', 
                         customers=customers,
                         categories=categories,
                         search=search,
                         selected_category=category,
                         selected_status=status)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        data = request.get_json()
        
        existing_customer = Customer.query.filter_by(phone=data['phone'], user_id=current_user.id).first()
        if existing_customer:
            return jsonify({'success': False, 'message': 'الزبون بهذا الرقم موجود مسبقاً'})
        
        customer = Customer(
            name=data['name'],
            phone=data['phone'],
            email=data.get('email', ''),
            address=data.get('address', ''),
            category=data.get('category', 'عادي'),
            notes=data.get('notes', ''),
            user_id=current_user.id
        )
        
        db.session.add(customer)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم إضافة الزبون بنجاح'})
    
    return render_template('add_customer.html')

@app.route('/customers/<int:customer_id>/delete', methods=['POST'])
@login_required
def delete_customer(customer_id):
    customer = Customer.query.filter_by(id=customer_id, user_id=current_user.id).first_or_404()
    
    Transaction.query.filter_by(customer_id=customer_id).delete()
    db.session.delete(customer)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم حذف الزبون بنجاح'})

@app.route('/transactions/add', methods=['POST'])
@login_required
def add_transaction():
    data = request.get_json()
    
    transaction = Transaction(
        customer_id=data['customer_id'],
        transaction_type=data['transaction_type'],
        amount=float(data['amount']),
        payment_method=data.get('payment_method', 'نقداً'),
        notes=data.get('notes', ''),
        user_id=current_user.id
    )
    
    customer = Customer.query.get(data['customer_id'])
    if customer:
        if data['transaction_type'] == 'بيع':
            customer.total_balance += float(data['amount'])
        else:  # تسديد
            customer.paid_amount += float(data['amount'])
        
        customer.pending_amount = max(0, customer.total_balance - customer.paid_amount)
    
    db.session.add(transaction)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'تم إضافة المعاملة بنجاح'})

@app.route('/reports')
@login_required
def reports():
    return render_template('reports.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

def open_browser():
    """فتح المتصفح تلقائياً بعد ثانيتين"""
    time.sleep(2)
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # Create admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
    
    # فتح المتصفح تلقائياً
    threading.Thread(target=open_browser, daemon=True).start()
    
    print("🚀 بدء تشغيل محلات أبو علاء CRM...")
    print("📱 سيتم فتح المتصفح تلقائياً خلال ثانيتين")
    print("🔗 الرابط: http://127.0.0.1:5000")
    print("👤 بيانات الدخول: admin / admin123")
    print("⏹️  اضغط Ctrl+C لإيقاف التطبيق")
    
    app.run(debug=False, host='127.0.0.1', port=5000) 