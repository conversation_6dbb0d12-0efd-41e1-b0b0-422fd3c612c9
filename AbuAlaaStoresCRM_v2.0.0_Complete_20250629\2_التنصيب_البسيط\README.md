# Abu Alaa Stores CRM - Desktop Application Suite

A comprehensive Customer Relationship Management (CRM) system designed specifically for Abu Alaa Stores, available in multiple desktop application formats for Windows.

## 🚀 Quick Start

### Option 1: One-Click Setup (Recommended)
```bash
# Install dependencies and build all versions
python install_dependencies.py
python build_all.py
```

### Option 2: Test Before Building
```bash
# Install dependencies
pip install -r requirements.txt

# Test the application
python app.py --desktop

# Run comprehensive tests
python test_application.py
```

## 📦 Available Versions

### 1. Flask + PyInstaller (Single Executable)
- **Best for**: Simple deployment, single file distribution
- **Size**: ~100MB
- **Build**: `python build_exe.py`
- **Output**: `dist/AbuAlaaStoresCRM.exe`

### 2. Desktop Launcher (GUI + System Tray)
- **Best for**: Better user experience, system integration
- **Features**: GUI launcher, system tray, notifications
- **Build**: `python build_desktop.py`
- **Output**: `dist_desktop/AbuAlaaStoresCRM_Desktop.exe`

### 3. Electron (Modern Desktop App)
- **Best for**: Professional deployment, modern UI
- **Features**: Native installer, auto-updates, cross-platform
- **Build**: `python build_electron.py`
- **Output**: `dist_electron/`

### 4. .NET WinForms (Native Windows)
- **Best for**: Maximum performance, true Windows integration
- **Features**: Native Windows app, small size, fast startup
- **Build**: `python build_dotnet.py`
- **Output**: `publish_dotnet/CRMDesktopApp.exe`

## 🛠️ System Requirements

### Development
- **OS**: Windows 7+ (64-bit recommended)
- **Python**: 3.8 or later
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 2GB free space
- **Optional**: Node.js 16+ (for Electron), .NET 6 SDK (for .NET version)

### End Users
- **OS**: Windows 7+ (64-bit recommended)
- **RAM**: 2GB minimum (4GB recommended)
- **Storage**: 500MB free space
- **Permissions**: Administrator for installation

## 🎯 Features

### Core Functionality
- 👥 **Customer Management** - Add, edit, search, and organize customers
- 💰 **Transaction Tracking** - Record sales, payments, and financial history
- 📊 **Financial Reports** - Generate Excel and PDF reports
- 📁 **Document Management** - Upload and organize customer documents
- 📞 **Communication Logs** - Track calls, emails, and meetings
- ⏰ **Reminders** - Set follow-up reminders and notifications
- 🔒 **User Management** - Multiple users with role-based access
- 🌐 **Multi-language** - Arabic and English support

### Technical Features
- 🗄️ **SQLite Database** - Local, reliable data storage
- 🔐 **Secure Authentication** - Password hashing and session management
- 📱 **Responsive Design** - Works on different screen sizes
- 🎨 **Modern UI** - Clean, professional interface
- 🔄 **Data Export** - Excel, PDF, and CSV export options
- 💾 **Automatic Backups** - Built-in data protection

## 📋 Build Instructions

### Prerequisites Check
```bash
# Check all prerequisites
python test_application.py
```

### Individual Builds

#### Flask + PyInstaller
```bash
pip install -r requirements.txt
python build_exe.py
```

#### Desktop Launcher
```bash
pip install pystray Pillow
python build_desktop.py
```

#### Electron
```bash
npm install
python build_exe.py  # Build backend first
npm run dist
```

#### .NET
```bash
dotnet restore
dotnet publish --configuration Release --runtime win-x64 --self-contained true
```

### Master Build (All Versions)
```bash
python build_all.py
```

## 🧪 Testing

### Automated Testing
```bash
python test_application.py
```

### Manual Testing
1. **Start the application**: `python app.py --desktop`
2. **Login**: admin / admin123
3. **Test core features**:
   - Add a customer
   - Record a transaction
   - Generate a report
   - Upload a document

## 📁 Project Structure

```
abu-alaa-crm/
├── app.py                    # Main Flask application
├── desktop_launcher.py       # GUI launcher with system tray
├── build_exe.py             # PyInstaller build script
├── build_desktop.py         # Desktop launcher build script
├── build_electron.py        # Electron build script
├── build_dotnet.py          # .NET build script
├── build_all.py             # Master build script
├── test_application.py      # Comprehensive test suite
├── install_dependencies.py  # Dependency installer
├── requirements.txt         # Python dependencies
├── package.json            # Node.js dependencies (Electron)
├── CRMDesktopApp.csproj    # .NET project file
├── templates/              # HTML templates
├── static/                 # CSS, JS, images
├── electron/               # Electron application files
├── Models/                 # .NET data models
├── Services/               # .NET services
└── uploads/                # File upload directory
```

## 🚀 Deployment

### For End Users
1. Choose the appropriate version for your needs
2. Download the distribution package
3. Run the installer or executable
4. Login with default credentials: admin / admin123
5. Change the password immediately
6. Start using the application

### For Developers
1. Use `build_all.py` to create all versions
2. Test on multiple Windows versions
3. Create proper application icons
4. Sign executables for production (optional)
5. Create installation packages

## 🔧 Configuration

### Database Location
Edit `app.py` to change database location:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///path/to/database.db'
```

### Server Port
Modify port in `app.py`:
```python
desktop_app.port = 8080  # Your preferred port
```

### Custom Branding
- Replace icons in respective directories
- Update titles and branding in source files
- Modify installer scripts

## 🐛 Troubleshooting

### Common Issues

#### "Python not found"
- Install Python 3.8+ from python.org
- Add Python to PATH during installation

#### "Module not found" errors
- Run: `pip install -r requirements.txt`
- Use virtual environment for isolation

#### Build fails
- Check prerequisites with `python test_application.py`
- Run as Administrator
- Check antivirus settings

#### Application won't start
- Check Windows Event Viewer
- Run from command prompt to see errors
- Verify all dependencies are included

## 📞 Support

### Getting Help
1. Check the [Setup Guide](SETUP_GUIDE.md)
2. Run the test suite: `python test_application.py`
3. Check build logs for specific errors
4. Contact development team

### Reporting Issues
Include:
- Windows version and architecture
- Python version
- Complete error messages
- Steps to reproduce

## 📄 License

This software is proprietary to Abu Alaa Stores.
Unauthorized distribution is prohibited.

## 🏗️ Development Team

Developed for Abu Alaa Stores
Version 2.0.0 - 2024

---

**For detailed setup instructions, see [SETUP_GUIDE.md](SETUP_GUIDE.md)**