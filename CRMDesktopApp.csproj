<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <AssemblyTitle>محلات أبو علاء - نظام إدارة الزبائن</AssemblyTitle>
    <AssemblyDescription>نظام إدارة الزبائن والمبيعات المحلي</AssemblyDescription>
    <AssemblyCompany>محلات أبو علاء</AssemblyCompany>
    <AssemblyProduct>نظام إدارة الزبائن</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>حقوق الطبع محفوظة © محلات أبو علاء 2024</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <StartupObject>CRMDesktopApp.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework Core for SQLite -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.0" />

    <!-- Configuration and Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.0" />

    <!-- Security -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />

    <!-- Excel and PDF Generation -->
    <PackageReference Include="ClosedXML" Version="0.102.0" />

    <!-- JSON Handling -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\**" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>
