#!/usr/bin/env python3
"""
Comprehensive Test Suite for Abu Alaa Stores CRM Desktop Application
Tests all variants and functionality
"""

import subprocess
import os
import sys
import time
import requests
import sqlite3
import json
import threading
from datetime import datetime

class CRMTester:
    def __init__(self):
        self.test_results = []
        self.server_process = None
        self.test_db_path = "test_crm.db"
        
    def log_test(self, test_name, result, details=""):
        """Log test result"""
        status = "PASS" if result else "FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = {
            'timestamp': timestamp,
            'test': test_name,
            'status': status,
            'details': details
        }
        self.test_results.append(log_entry)
        print(f"[{timestamp}] {status}: {test_name}")
        if details and not result:
            print(f"    Details: {details}")
    
    def test_python_environment(self):
        """Test Python environment and dependencies"""
        print("\n🐍 Testing Python Environment")
        print("-" * 40)
        
        # Test Python version
        version = sys.version_info
        result = version.major >= 3 and version.minor >= 8
        self.log_test("Python Version (3.8+)", result, 
                     f"Found: {version.major}.{version.minor}.{version.micro}")
        
        # Test required modules
        required_modules = [
            'flask', 'flask_sqlalchemy', 'flask_login', 'werkzeug',
            'openpyxl', 'reportlab', 'qrcode', 'PIL', 'bcrypt', 'jwt'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                self.log_test(f"Module: {module}", True)
            except ImportError as e:
                self.log_test(f"Module: {module}", False, str(e))
    
    def test_database_operations(self):
        """Test database creation and operations"""
        print("\n🗄️  Testing Database Operations")
        print("-" * 40)
        
        try:
            # Remove test database if exists
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
            
            # Test database creation
            conn = sqlite3.connect(self.test_db_path)
            cursor = conn.cursor()
            
            # Create test table
            cursor.execute('''
                CREATE TABLE test_customers (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    phone TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.log_test("Database Creation", True)
            
            # Test insert
            cursor.execute(
                "INSERT INTO test_customers (name, phone) VALUES (?, ?)",
                ("Test Customer", "1234567890")
            )
            conn.commit()
            self.log_test("Database Insert", True)
            
            # Test select
            cursor.execute("SELECT * FROM test_customers WHERE name = ?", ("Test Customer",))
            result = cursor.fetchone()
            self.log_test("Database Select", result is not None)
            
            # Test update
            cursor.execute(
                "UPDATE test_customers SET name = ? WHERE phone = ?",
                ("Updated Customer", "1234567890")
            )
            conn.commit()
            self.log_test("Database Update", cursor.rowcount > 0)
            
            # Test delete
            cursor.execute("DELETE FROM test_customers WHERE phone = ?", ("1234567890",))
            conn.commit()
            self.log_test("Database Delete", cursor.rowcount > 0)
            
            conn.close()
            
        except Exception as e:
            self.log_test("Database Operations", False, str(e))
        finally:
            # Cleanup
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
    
    def test_flask_application(self):
        """Test Flask application startup and basic functionality"""
        print("\n🌐 Testing Flask Application")
        print("-" * 40)
        
        try:
            # Start Flask server in background
            env = os.environ.copy()
            env['FLASK_ENV'] = 'testing'
            
            self.server_process = subprocess.Popen(
                [sys.executable, 'app.py', '--desktop'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            
            # Wait for server to start
            time.sleep(5)
            
            # Test server response
            try:
                response = requests.get('http://127.0.0.1:5000', timeout=10)
                self.log_test("Flask Server Start", response.status_code == 200)
                
                # Test login page
                response = requests.get('http://127.0.0.1:5000/login', timeout=5)
                self.log_test("Login Page Access", response.status_code == 200)
                
                # Test API endpoint
                response = requests.post(
                    'http://127.0.0.1:5000/login',
                    json={'username': 'admin', 'password': 'admin123'},
                    timeout=5
                )
                self.log_test("Login API", response.status_code == 200)
                
            except requests.exceptions.RequestException as e:
                self.log_test("Flask Server Communication", False, str(e))
            
        except Exception as e:
            self.log_test("Flask Application Start", False, str(e))
        finally:
            # Stop server
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait()
    
    def test_build_outputs(self):
        """Test if build outputs exist and are functional"""
        print("\n🔨 Testing Build Outputs")
        print("-" * 40)
        
        # Test Flask + PyInstaller build
        flask_exe = os.path.join('dist', 'AbuAlaaStoresCRM.exe')
        if os.path.exists(flask_exe):
            self.log_test("Flask Executable Exists", True)
            # Test if executable runs (quick test)
            try:
                proc = subprocess.Popen([flask_exe], stdout=subprocess.PIPE, 
                                      stderr=subprocess.PIPE)
                time.sleep(3)  # Let it start
                proc.terminate()
                proc.wait()
                self.log_test("Flask Executable Runs", True)
            except Exception as e:
                self.log_test("Flask Executable Runs", False, str(e))
        else:
            self.log_test("Flask Executable Exists", False, "File not found")
        
        # Test Desktop Launcher build
        desktop_exe = os.path.join('dist_desktop', 'AbuAlaaStoresCRM_Desktop.exe')
        if os.path.exists(desktop_exe):
            self.log_test("Desktop Launcher Exists", True)
        else:
            self.log_test("Desktop Launcher Exists", False, "File not found")
        
        # Test Electron build
        electron_dir = 'dist_electron'
        if os.path.exists(electron_dir):
            self.log_test("Electron Build Exists", True)
        else:
            self.log_test("Electron Build Exists", False, "Directory not found")
        
        # Test .NET build
        dotnet_exe = os.path.join('publish_dotnet', 'CRMDesktopApp.exe')
        if os.path.exists(dotnet_exe):
            self.log_test(".NET Executable Exists", True)
        else:
            self.log_test(".NET Executable Exists", False, "File not found")
    
    def test_file_permissions(self):
        """Test file permissions and access"""
        print("\n🔐 Testing File Permissions")
        print("-" * 40)
        
        # Test write permissions in current directory
        test_file = "permission_test.tmp"
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            self.log_test("Write Permissions", True)
        except Exception as e:
            self.log_test("Write Permissions", False, str(e))
        
        # Test uploads directory
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            try:
                os.makedirs(uploads_dir)
                self.log_test("Create Uploads Directory", True)
            except Exception as e:
                self.log_test("Create Uploads Directory", False, str(e))
        else:
            self.log_test("Uploads Directory Exists", True)
    
    def test_system_requirements(self):
        """Test system requirements"""
        print("\n💻 Testing System Requirements")
        print("-" * 40)
        
        # Test available disk space
        import shutil
        try:
            total, used, free = shutil.disk_usage(".")
            free_gb = free // (1024**3)
            self.log_test("Disk Space (>1GB)", free_gb > 1, f"{free_gb}GB available")
        except Exception as e:
            self.log_test("Disk Space Check", False, str(e))
        
        # Test Windows version
        if os.name == 'nt':
            try:
                import platform
                version = platform.version()
                self.log_test("Windows OS", True, f"Version: {version}")
            except Exception as e:
                self.log_test("Windows OS", False, str(e))
        else:
            self.log_test("Windows OS", False, "Not running on Windows")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Abu Alaa Stores CRM - Comprehensive Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run test suites
        self.test_python_environment()
        self.test_database_operations()
        self.test_flask_application()
        self.test_build_outputs()
        self.test_file_permissions()
        self.test_system_requirements()
        
        # Generate report
        self.generate_test_report(time.time() - start_time)
    
    def generate_test_report(self, duration):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for test in self.test_results if test['status'] == 'PASS')
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.1f} seconds")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests ({failed_tests}):")
            for test in self.test_results:
                if test['status'] == 'FAIL':
                    print(f"  - {test['test']}")
                    if test['details']:
                        print(f"    {test['details']}")
        
        # Save detailed report
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'duration': duration,
            'summary': {
                'total': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': (passed_tests/total_tests)*100
            },
            'tests': self.test_results
        }
        
        with open('test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {os.path.abspath('test_report.json')}")
        
        # Recommendations
        print(f"\n📋 Recommendations:")
        if failed_tests == 0:
            print("✅ All tests passed! The application is ready for deployment.")
        else:
            print("⚠️  Some tests failed. Please address the issues before deployment:")
            print("   1. Check error details above")
            print("   2. Install missing dependencies")
            print("   3. Fix permission issues")
            print("   4. Re-run tests after fixes")

def main():
    """Main test function"""
    tester = CRMTester()
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test suite error: {e}")
    finally:
        # Cleanup
        if tester.server_process:
            tester.server_process.terminate()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
