# Abu Alaa Stores CRM - Complete Setup Guide

## Overview

This guide will help you set up and build the Abu Alaa Stores CRM Desktop Application. The project supports multiple deployment options:

1. **Flask + PyInstaller** - Single executable file
2. **Desktop Launcher** - GUI launcher with system tray
3. **Electron** - Modern desktop application with installer
4. **NET** - Native Windows application

## Prerequisites

### Required Software

#### For All Builds
- **Python 3.8+** - [Download from python.org](https://www.python.org/downloads/)
- **Git** - [Download from git-scm.com](https://git-scm.com/downloads)

#### For Electron Build (Optional)
- **Node.js 16+** - [Download from nodejs.org](https://nodejs.org/)
- **npm** (comes with Node.js)

#### For .NET Build (Optional)
- **.NET 6 SDK** - [Download from microsoft.com](https://dotnet.microsoft.com/download/dotnet/6.0)

### System Requirements
- **OS**: Windows 7 or later (64-bit recommended)
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 2GB free space for development, 500MB for deployment
- **Permissions**: Administrator privileges for installation

## Quick Start (Recommended)

### Option 1: Automated Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd abu-alaa-crm
   ```

2. **Run the dependency installer**
   ```bash
   python install_dependencies.py
   ```

3. **Build all versions**
   ```bash
   python build_all.py
   ```

4. **Choose option 5** (All available versions) when prompted

### Option 2: Manual Setup

1. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Test the Flask application**
   ```bash
   python app.py --desktop
   ```

3. **Build your preferred version**
   ```bash
   # Flask + PyInstaller
   python build_exe.py
   
   # Desktop Launcher
   python build_desktop.py
   
   # Electron (requires Node.js)
   python build_electron.py
   
   # .NET (requires .NET SDK)
   python build_dotnet.py
   ```

## Detailed Build Instructions

### Flask + PyInstaller Build

**Best for**: Simple deployment, single executable file

```bash
# Install dependencies
pip install -r requirements.txt

# Build executable
python build_exe.py

# Output: dist/AbuAlaaStoresCRM.exe
```

**Features**:
- ✅ Single executable file
- ✅ No additional runtime required
- ✅ Quick deployment
- ❌ Larger file size (~100MB)
- ❌ Web-based interface

### Desktop Launcher Build

**Best for**: Better user experience with system tray integration

```bash
# Install additional dependencies
pip install pystray Pillow

# Build desktop launcher
python build_desktop.py

# Output: dist_desktop/AbuAlaaStoresCRM_Desktop.exe
```

**Features**:
- ✅ GUI launcher interface
- ✅ System tray integration
- ✅ Better desktop experience
- ❌ Requires Python runtime
- ❌ More complex deployment

### Electron Build

**Best for**: Professional deployment with modern UI

```bash
# Install Node.js dependencies
npm install

# Build Python backend first
python build_exe.py

# Build Electron application
npm run dist

# Output: dist_electron/
```

**Features**:
- ✅ Modern, native-looking interface
- ✅ Professional installer
- ✅ Cross-platform potential
- ✅ Rich desktop integration
- ❌ Larger size (~200MB)
- ❌ Higher memory usage

### .NET Build

**Best for**: True native Windows performance

```bash
# Restore packages
dotnet restore

# Build release version
dotnet build --configuration Release

# Publish self-contained
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output publish

# Output: publish/CRMDesktopApp.exe
```

**Features**:
- ✅ True native Windows application
- ✅ Excellent performance
- ✅ Small executable size
- ✅ Deep Windows integration
- ❌ Windows-only
- ❌ Requires complete rewrite

## Testing Your Build

### 1. Test the Flask Application
```bash
python app.py --desktop
```
- Should open browser automatically
- Default login: admin / admin123
- Test basic functionality

### 2. Test Built Executables
- Run the executable from command line first
- Check for error messages
- Test on a clean system without development tools
- Verify database creation and data persistence

### 3. Test Installation
- Create a test user account
- Install as non-administrator
- Test all features thoroughly

## Deployment

### For End Users

1. **Choose the appropriate version** based on your needs
2. **Copy the distribution folder** to target systems
3. **Run the executable** or installer
4. **First-time setup**:
   - Application creates database automatically
   - Login with: admin / admin123
   - Change password immediately
   - Add your first customer to test

### For Developers

1. **Use the distribution package** created by `build_all.py`
2. **Test on multiple Windows versions**
3. **Create proper application icons**
4. **Sign executables** for production (optional)
5. **Create installation documentation**

## Troubleshooting

### Common Issues

#### "Python not found"
- Install Python 3.8+ from python.org
- Add Python to PATH during installation
- Restart command prompt

#### "Module not found" errors
- Run: `pip install -r requirements.txt`
- Use virtual environment: `python -m venv venv`
- Activate: `venv\Scripts\activate` (Windows)

#### "Node.js not found"
- Install Node.js 16+ from nodejs.org
- Restart command prompt
- Verify: `node --version`

#### ".NET SDK not found"
- Install .NET 6 SDK from microsoft.com
- Restart command prompt
- Verify: `dotnet --version`

#### Build fails with permission errors
- Run command prompt as Administrator
- Check antivirus software settings
- Ensure sufficient disk space

#### Application won't start
- Check Windows Event Viewer for errors
- Run from command prompt to see error messages
- Verify all dependencies are included
- Test on clean system

### Performance Issues

#### Large executable size
- Use framework-dependent builds when possible
- Remove unused dependencies
- Consider Electron for better size/feature ratio

#### Slow startup
- Use SSD storage
- Exclude from antivirus real-time scanning
- Consider .NET version for fastest startup

#### High memory usage
- Close unnecessary applications
- Use 64-bit Windows
- Consider Flask version for lower memory usage

## Advanced Configuration

### Custom Database Location
Edit `app.py` and modify the database path:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///path/to/your/database.db'
```

### Custom Port
Modify the port in `app.py`:
```python
desktop_app.port = 8080  # Your preferred port
```

### Custom Branding
- Replace application icons in respective folders
- Modify titles and branding in source files
- Update installer scripts with your information

## Support

### Getting Help
1. Check this guide first
2. Review build logs for specific errors
3. Test individual components
4. Contact development team with:
   - Operating system version
   - Python version
   - Complete error messages
   - Steps to reproduce

### Reporting Issues
Include the following information:
- Windows version and architecture
- Python version (`python --version`)
- Complete error message
- Build log file
- Steps to reproduce the issue

## License

This software is proprietary to Abu Alaa Stores.
Unauthorized distribution is prohibited.

---

**Last Updated**: 2024
**Version**: 2.0.0
