# دليل ملفات التنصيب - نظام إدارة الزبائن

## 📦 أنواع ملفات التنصيب المتاحة

### 1. التنصيب المتقدم (Inno Setup) - الأفضل للاستخدام المهني
**الملف**: `installer_output/AbuAlaaStoresCRM_Setup_v2.0.0.exe`

**المميزات**:
- ✅ واجهة تنصيب احترافية
- ✅ إنشاء اختصارات تلقائياً
- ✅ إضافة لقائمة البرامج
- ✅ ملف إلغاء تثبيت
- ✅ فحص المتطلبات تلقائياً
- ✅ دعم التحديث التلقائي

**المتطلبات**:
- Inno Setup مثبت على جهاز التطوير
- صلاحيات المدير للتثبيت

**طريقة الإنشاء**:
```bash
python create_installer.py
```

### 2. التنصيب البسيط - لا يحتاج برامج إضافية
**الملف**: `simple_installer/install.bat`

**المميزات**:
- ✅ لا يحتاج برامج إضافية
- ✅ سكريبت batch بسيط
- ✅ إنشاء اختصارات
- ✅ ملف إلغاء تثبيت
- ✅ يعمل على جميع إصدارات Windows

**طريقة الاستخدام**:
1. انقر بالزر الأيمن على `install.bat`
2. اختر "تشغيل كمدير"
3. اتبع التعليمات

**طريقة الإنشاء**:
```bash
python create_simple_installer.py
```

### 3. النسخة المحمولة - لا تحتاج تثبيت
**المجلد**: `portable_version/`

**المميزات**:
- ✅ لا تحتاج تثبيت
- ✅ تعمل من أي مجلد
- ✅ يمكن نسخها على فلاش ميموري
- ✅ جميع البيانات محفوظة محلياً
- ✅ سهلة النقل والنسخ الاحتياطي

**طريقة الاستخدام**:
1. انسخ المجلد إلى أي مكان
2. شغل `تشغيل_البرنامج.bat`
3. أو شغل `AbuAlaaStoresCRM.exe` مباشرة

### 4. حزمة التوزيع الشاملة
**الملف**: `AbuAlaaStoresCRM_v2.0.0_Complete_YYYYMMDD.zip`

**المحتويات**:
- 📁 التنصيب المتقدم
- 📁 التنصيب البسيط  
- 📁 النسخة المحمولة
- 📁 التوثيق الكامل

## 🛠️ إنشاء ملفات التنصيب

### الطريقة السريعة (جميع الأنواع)
```bash
# بناء التطبيق أولاً
python build_exe.py

# إنشاء جميع ملفات التنصيب
python create_all_installers.py
```

### الطرق الفردية

#### 1. التنصيب المتقدم فقط
```bash
# يتطلب Inno Setup
python create_installer.py
```

#### 2. التنصيب البسيط فقط
```bash
python create_simple_installer.py
```

#### 3. النسخة المحمولة فقط
```bash
# ضمن create_all_installers.py
python -c "from create_all_installers import InstallerCreator; c = InstallerCreator(); c.create_portable_version()"
```

## 📋 متطلبات الإنشاء

### للتطوير
- **Python 3.8+**
- **PyInstaller** (لبناء التطبيق)
- **Inno Setup** (اختياري - للتنصيب المتقدم)

### للمستخدم النهائي
- **Windows 7+** (64-bit مفضل)
- **2GB RAM** كحد أدنى
- **500MB** مساحة فارغة
- **صلاحيات المدير** (للتنصيب فقط)

## 🔧 تخصيص ملفات التنصيب

### تغيير معلومات التطبيق
عدّل الملفات التالية:
- `installer.iss` - إعدادات Inno Setup
- `LICENSE.txt` - نص الترخيص
- `INSTALL_INFO.txt` - معلومات ما قبل التثبيت
- `AFTER_INSTALL.txt` - معلومات ما بعد التثبيت

### إضافة أيقونة مخصصة
1. ضع ملف `icon.ico` في المجلد الرئيسي
2. أعد إنشاء ملفات التنصيب

### تغيير اسم التطبيق
عدّل المتغيرات في:
- `installer.iss` - السطر `AppName=`
- `create_installer.py` - متغير `app_name`
- `create_simple_installer.py` - في النصوص

## 🧪 اختبار ملفات التنصيب

### اختبار أساسي
1. **اختبر على جهاز نظيف** (بدون Python)
2. **جرب كمستخدم عادي** وكمدير
3. **اختبر إلغاء التثبيت**
4. **تأكد من عمل الاختصارات**

### اختبار متقدم
```bash
# اختبار شامل للتطبيق
python test_application.py
```

### قائمة فحص التنصيب
- [ ] يعمل الملف التنفيذي
- [ ] تفتح الواجهة في المتصفح
- [ ] بيانات الدخول الافتراضية تعمل
- [ ] يمكن إضافة زبون جديد
- [ ] يمكن إنشاء تقرير
- [ ] الاختصارات تعمل
- [ ] إلغاء التثبيت يعمل

## 🚀 التوزيع

### للاستخدام الداخلي
- استخدم **النسخة المحمولة** للسهولة
- أو **التنصيب البسيط** للتثبيت الدائم

### للتوزيع المهني
- استخدم **التنصيب المتقدم** (Inno Setup)
- وقّع الملف رقمياً (اختياري)
- اختبر على أنظمة مختلفة

### للتوزيع الشامل
- استخدم **حزمة التوزيع الشاملة**
- تحتوي على جميع الخيارات
- مناسبة للمستخدمين المختلفين

## 🔒 الأمان

### توقيع الملفات (اختياري)
```bash
# باستخدام signtool (Windows SDK)
signtool sign /f certificate.pfx /p password /t http://timestamp.digicert.com AbuAlaaStoresCRM_Setup.exe
```

### فحص مكافح الفيروسات
- اختبر الملفات مع مكافحات فيروسات مختلفة
- أضف استثناءات إذا لزم الأمر
- استخدم خدمات فحص أونلاين (VirusTotal)

## 📞 الدعم الفني

### مشاكل شائعة

#### "لا يمكن تشغيل الملف"
- تأكد من تشغيله كمدير
- تحقق من إعدادات مكافح الفيروسات
- جرب على جهاز آخر

#### "فشل التنصيب"
- تأكد من وجود مساحة كافية
- أغلق البرامج الأخرى
- أعد تشغيل الكمبيوتر وحاول مرة أخرى

#### "البرنامج لا يعمل بعد التثبيت"
- تحقق من ملفات السجل
- جرب تشغيله كمدير
- أعد تثبيت البرنامج

### الحصول على المساعدة
1. راجع `SETUP_GUIDE.md` للتعليمات المفصلة
2. شغل `test_application.py` لفحص المشاكل
3. تواصل مع فريق التطوير

---

## 📝 ملاحظات مهمة

### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- ⚠️ **مهم**: غيّر كلمة المرور فوراً بعد الدخول الأول!

### النسخ الاحتياطية
- احتفظ بنسخة من قاعدة البيانات (`crm.db`)
- انسخ مجلد `uploads` للمستندات
- استخدم ميزة تصدير البيانات في التطبيق

### التحديثات
- احتفظ بالإصدارات القديمة قبل التحديث
- اختبر الإصدار الجديد على بيانات تجريبية
- اعمل نسخة احتياطية قبل التحديث

---

**© 2024 محلات أبو علاء - جميع الحقوق محفوظة**
