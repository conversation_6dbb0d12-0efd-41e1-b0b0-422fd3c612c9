{% extends "base.html" %}

{% block title %}محلات أبو علاء - الصفحة الرئيسية{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">
            <i class="fas fa-store"></i>
            محلات أبو علاء
        </h1>
        <p class="hero-subtitle">
            نظام إدارة الزبائن والمبيعات والمشتريات لمحلات أبو علاء
        </p>
        <div class="hero-features">
            <div class="feature">
                <i class="fas fa-mobile-alt"></i>
                <span>متوافق مع الهواتف المحمولة</span>
            </div>
            <div class="feature">
                <i class="fas fa-shield-alt"></i>
                <span>حماية البيانات</span>
            </div>
            <div class="feature">
                <i class="fas fa-chart-line"></i>
                <span>تقارير مفصلة</span>
            </div>
            <div class="feature">
                <i class="fas fa-sync-alt"></i>
                <span>مزامنة سحابية</span>
            </div>
        </div>
        <div class="hero-actions">
            <a href="{{ url_for('login') }}" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </a>
            <a href="{{ url_for('register') }}" class="btn btn-secondary">
                <i class="fas fa-user-plus"></i>
                إنشاء حساب جديد
            </a>
        </div>
    </div>
    <div class="hero-image">
        <i class="fas fa-chart-pie"></i>
    </div>
</div>

<div class="features-section">
    <h2>المميزات الرئيسية</h2>
    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3>إدارة الزبائن</h3>
            <p>إضافة وتعديل وحذف بيانات الزبائن مع تصنيفات مخصصة</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3>المبيعات والتسديدات</h3>
            <p>تسجيل عمليات البيع والتسديد مع تتبع الأرصدة</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3>إدارة المدفوعات</h3>
            <p>تتبع المدفوعات والأرصدة المعلقة</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-phone"></i>
            </div>
            <h3>سجل التواصل</h3>
            <p>تسجيل جميع أنواع التواصل مع الزبائن</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <h3>المستندات</h3>
            <p>رفع وإدارة المستندات والملفات</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-bell"></i>
            </div>
            <h3>التذكيرات</h3>
            <p>إعداد تذكيرات للمدفوعات والمواعيد</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <h3>التقارير</h3>
            <p>تقارير مفصلة مع إمكانية التصدير</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3>تطبيق ويب</h3>
            <p>يمكن تثبيته كتطبيق على الهاتف</p>
        </div>
    </div>
</div>

<div class="cta-section">
    <h2>ابدأ الآن</h2>
    <p>انضم إلى محلات أبو علاء لإدارة زبائنك بسهولة</p>
    <a href="{{ url_for('register') }}" class="btn btn-primary btn-large">
        <i class="fas fa-rocket"></i>
        ابدأ مجاناً
    </a>
</div>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.hero-image {
    position: relative;
    height: 400px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    font-size: 2rem;
}

.floating-card span {
    color: white;
    font-weight: 500;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: 20%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.features-section {
    background: #1a1a1a;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h4 {
    color: white;
    margin-bottom: 15px;
}

.feature-card p {
    color: #adb5bd;
    margin: 0;
}

.cta-section {
    background: linear-gradient(135deg, #007bff, #0056b3);
}
</style>
{% endblock %} 