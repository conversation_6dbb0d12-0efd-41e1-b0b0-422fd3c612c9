{"timestamp": "2025-06-29T18:14:33.265081", "duration": 16.85828399658203, "summary": {"total": 25, "passed": 20, "failed": 5, "success_rate": 80.0}, "tests": [{"timestamp": "18:14:16", "test": "Python Version (3.8+)", "status": "PASS", "details": "Found: 3.13.5"}, {"timestamp": "18:14:18", "test": "Module: flask", "status": "PASS", "details": ""}, {"timestamp": "18:14:20", "test": "Module: flask_sqlalchemy", "status": "PASS", "details": ""}, {"timestamp": "18:14:20", "test": "Module: flask_login", "status": "PASS", "details": ""}, {"timestamp": "18:14:20", "test": "Module: werkzeug", "status": "PASS", "details": ""}, {"timestamp": "18:14:24", "test": "Module: openpyxl", "status": "PASS", "details": ""}, {"timestamp": "18:14:24", "test": "Module: reportlab", "status": "PASS", "details": ""}, {"timestamp": "18:14:24", "test": "Module: qrcode", "status": "PASS", "details": ""}, {"timestamp": "18:14:24", "test": "Module: PIL", "status": "PASS", "details": ""}, {"timestamp": "18:14:24", "test": "Module: bcrypt", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Module: jwt", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Database Creation", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Database Insert", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Database Select", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Database Update", "status": "PASS", "details": ""}, {"timestamp": "18:14:26", "test": "Database Delete", "status": "PASS", "details": ""}, {"timestamp": "18:14:33", "test": "Flask Server Communication", "status": "FAIL", "details": "HTTPConnectionPool(host='127.0.0.1', port=5000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002ABF8C20590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, {"timestamp": "18:14:33", "test": "Flask Executable Exists", "status": "FAIL", "details": "File not found"}, {"timestamp": "18:14:33", "test": "Desktop Launcher Exists", "status": "FAIL", "details": "File not found"}, {"timestamp": "18:14:33", "test": "Electron Build Exists", "status": "FAIL", "details": "Directory not found"}, {"timestamp": "18:14:33", "test": ".NET Executable Exists", "status": "FAIL", "details": "File not found"}, {"timestamp": "18:14:33", "test": "Write Permissions", "status": "PASS", "details": ""}, {"timestamp": "18:14:33", "test": "Uploads Directory Exists", "status": "PASS", "details": ""}, {"timestamp": "18:14:33", "test": "Disk Space (>1GB)", "status": "PASS", "details": "121GB available"}, {"timestamp": "18:14:33", "test": "Windows OS", "status": "PASS", "details": "Version: 10.0.22631"}]}