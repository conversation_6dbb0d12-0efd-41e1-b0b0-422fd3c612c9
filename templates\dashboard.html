{% extends "base.html" %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>لوحة التحكم</h1>
        <p>مرحباً {{ current_user.username }}، إليك ملخص نشاطك</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>{{ total_customers }}</h3>
                <p>إجمالي الزبائن</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
                <h3>{{ active_customers }}</h3>
                <p>الزبائن النشطون</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-content">
                <h3>{{ "%.0f"|format(total_pending) }} د.ع</h3>
                <p>إجمالي المعلق</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <h3>{{ "%.0f"|format(total_pending / total_customers if total_customers > 0 else 0) }} د.ع</h3>
                <p>متوسط المعلق للزبون</p>
            </div>
        </div>
    </div>

    <div class="dashboard-content">
        <div class="dashboard-section">
            <div class="section-header">
                <h2>أحدث المعاملات</h2>
                <a href="#" class="btn btn-primary btn-sm">عرض الكل</a>
            </div>
            <div class="transactions-list">
                {% if recent_transactions %}
                    {% for transaction in recent_transactions %}
                    <div class="transaction-item">
                        <div class="transaction-icon">
                            {% if transaction.transaction_type == 'بيع' %}
                                <i class="fas fa-arrow-up text-success"></i>
                            {% else %}
                                <i class="fas fa-arrow-down text-danger"></i>
                            {% endif %}
                        </div>
                        <div class="transaction-details">
                            <h4>{{ transaction.transaction_type }}</h4>
                            <p>{{ "%.0f"|format(transaction.amount) }} د.ع - {{ transaction.payment_method }}</p>
                            <small>{{ transaction.transaction_date.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                        <div class="transaction-amount">
                            <span class="amount {{ 'positive' if transaction.transaction_type == 'بيع' else 'negative' }}">
                                {{ "%.0f"|format(transaction.amount) }} د.ع
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>لا توجد معاملات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="dashboard-section">
            <div class="section-header">
                <h2>الزبائن مع المدفوعات المعلقة</h2>
                <a href="{{ url_for('customers') }}" class="btn btn-primary btn-sm">عرض الكل</a>
            </div>
            <div class="customers-list">
                {% if customers_with_pending %}
                    {% for customer in customers_with_pending %}
                    <div class="customer-item">
                        <div class="customer-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="customer-details">
                            <h4>{{ customer.name }}</h4>
                            <p>{{ customer.phone }}</p>
                            <small>{{ customer.category }}</small>
                        </div>
                        <div class="customer-balance">
                            <span class="balance pending">{{ "%.0f"|format(customer.pending_amount) }} د.ع</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <p>جميع الزبائن محدثون</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="quick-actions">
        <h2>إجراءات سريعة</h2>
        <div class="actions-grid">
            <a href="{{ url_for('add_customer') }}" class="action-card">
                <i class="fas fa-user-plus"></i>
                <span>إضافة زبون جديد</span>
            </a>
            <a href="#" class="action-card" onclick="showTransactionModal()">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة معاملة</span>
            </a>
            <a href="{{ url_for('reports') }}" class="action-card">
                <i class="fas fa-chart-bar"></i>
                <span>عرض التقارير</span>
            </a>
            <a href="#" class="action-card">
                <i class="fas fa-bell"></i>
                <span>إعداد التذكيرات</span>
            </a>
        </div>
    </div>
</div>

<!-- Transaction Modal -->
<div id="transactionModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>إضافة معاملة جديدة</h3>
            <span class="close">&times;</span>
        </div>
        <form id="transactionForm">
            <div class="form-group">
                <label for="customer_id">الزبون</label>
                <select id="customer_id" name="customer_id" required>
                    <option value="">اختر الزبون</option>
                    {% for customer in all_customers %}
                    <option value="{{ customer.id }}">{{ customer.name }} - {{ customer.phone }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="transaction_type">نوع المعاملة</label>
                <select id="transaction_type" name="transaction_type" required>
                    <option value="بيع">بيع</option>
                    <option value="تسديد">تسديد</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="amount">المبلغ</label>
                <input type="number" id="amount" name="amount" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="payment_method">طريقة الدفع</label>
                <select id="payment_method" name="payment_method">
                    <option value="نقداً">نقداً</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="شيك">شيك</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="notes">ملاحظات</label>
                <textarea id="notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeTransactionModal()">إلغاء</button>
                <button type="submit" class="btn btn-primary">إضافة المعاملة</button>
            </div>
        </form>
    </div>
</div>

<script>
function showTransactionModal() {
    document.getElementById('transactionModal').style.display = 'block';
}

function closeTransactionModal() {
    document.getElementById('transactionModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('transactionModal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

// Close modal when clicking X
document.querySelector('.close').onclick = function() {
    closeTransactionModal();
}

// Handle transaction form submission
document.getElementById('transactionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        customer_id: document.getElementById('customer_id').value,
        transaction_type: document.getElementById('transaction_type').value,
        amount: document.getElementById('amount').value,
        payment_method: document.getElementById('payment_method').value,
        notes: document.getElementById('notes').value
    };
    
    fetch('/transactions/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeTransactionModal();
            // Reload page to update dashboard
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ في الاتصال', 'error');
    });
});
</script>
{% endblock %} 