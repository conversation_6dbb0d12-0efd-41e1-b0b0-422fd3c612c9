{% extends "base.html" %}

{% block title %}إدارة الزبائن{% endblock %}

{% block content %}
<div class="customers-page">
    <div class="page-header">
        <div class="header-content">
            <h1>إدارة الزبائن</h1>
            <p>إدارة بيانات الزبائن والمعاملات</p>
        </div>
        <div class="header-actions">
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                إضافة زبون جديد
            </a>
        </div>
    </div>

    <div class="filters-section">
        <form method="GET" class="filters-form">
            <div class="search-box">
                <input type="text" name="search" value="{{ search }}" placeholder="البحث في الزبائن...">
                <button type="submit">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            
            <div class="filter-options">
                <select name="category">
                    <option value="">جميع الفئات</option>
                    {% for category in categories %}
                    <option value="{{ category[0] }}" {% if category[0] == selected_category %}selected{% endif %}>
                        {{ category[0] }}
                    </option>
                    {% endfor %}
                </select>
                
                <select name="status">
                    <option value="">جميع الحالات</option>
                    <option value="نشط" {% if selected_status == 'نشط' %}selected{% endif %}>نشط</option>
                    <option value="غير نشط" {% if selected_status == 'غير نشط' %}selected{% endif %}>غير نشط</option>
                    <option value="محظور" {% if selected_status == 'محظور' %}selected{% endif %}>محظور</option>
                </select>
                
                <button type="submit" class="btn btn-secondary">تطبيق</button>
            </div>
        </form>
    </div>

    <div class="customers-list">
        {% if customers.items %}
            {% for customer in customers.items %}
            <div class="customer-card">
                <div class="customer-info">
                    <div class="customer-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="customer-details">
                        <h3>{{ customer.name }}</h3>
                        <p class="customer-phone">
                            <i class="fas fa-phone"></i>
                            {{ customer.phone }}
                        </p>
                        {% if customer.email %}
                        <p class="customer-email">
                            <i class="fas fa-envelope"></i>
                            {{ customer.email }}
                        </p>
                        {% endif %}
                        <div class="customer-meta">
                            <span class="category">{{ customer.category }}</span>
                            <span class="status status-{{ customer.status.lower() }}">{{ customer.status }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="customer-balance">
                    <div class="balance-item">
                        <span class="label">إجمالي الرصيد:</span>
                        <span class="amount">{{ "%.0f"|format(customer.total_balance) }} د.ع</span>
                    </div>
                    <div class="balance-item">
                        <span class="label">المدفوع:</span>
                        <span class="amount paid">{{ "%.0f"|format(customer.paid_amount) }} د.ع</span>
                    </div>
                    <div class="balance-item">
                        <span class="label">المعلق:</span>
                        <span class="amount pending">{{ "%.0f"|format(customer.pending_amount) }} د.ع</span>
                    </div>
                </div>
                
                <div class="customer-actions">
                    <a href="{{ url_for('customer_detail', customer_id=customer.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{{ url_for('edit_customer', customer_id=customer.id) }}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer({{ customer.id }}, '{{ customer.name }}')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
            {% endfor %}
            
            <!-- Pagination -->
            {% if customers.pages > 1 %}
            <div class="pagination">
                {% if customers.has_prev %}
                <a href="{{ url_for('customers', page=customers.prev_num, search=search, category=selected_category, status=selected_status) }}" class="page-link">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </a>
                {% endif %}
                
                <span class="page-info">
                    صفحة {{ customers.page }} من {{ customers.pages }}
                </span>
                
                {% if customers.has_next %}
                <a href="{{ url_for('customers', page=customers.next_num, search=search, category=selected_category, status=selected_status) }}" class="page-link">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </a>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>لا توجد زبائن</h3>
                <p>ابدأ بإضافة زبونك الأول</p>
                <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    إضافة زبون جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function deleteCustomer(customerId, customerName) {
    if (confirm(`هل أنت متأكد من حذف الزبون "${customerName}"؟`)) {
        fetch(`/customers/${customerId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ في الاتصال', 'error');
        });
    }
}
</script>
{% endblock %} 