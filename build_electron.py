#!/usr/bin/env python3
"""
Build script for Abu Alaa Stores CRM Electron Desktop Application
Creates a professional desktop application with installer
"""

import subprocess
import os
import sys
import shutil
import json

def check_node_npm():
    """Check if Node.js and npm are installed"""
    try:
        node_version = subprocess.check_output(['node', '--version'], text=True).strip()
        npm_version = subprocess.check_output(['npm', '--version'], text=True).strip()
        print(f"✅ Node.js {node_version} found")
        print(f"✅ npm {npm_version} found")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js and npm are required but not found")
        print("Please install Node.js from https://nodejs.org/")
        return False

def install_dependencies():
    """Install npm dependencies"""
    print("\n📦 Installing npm dependencies...")
    try:
        subprocess.check_call(['npm', 'install'], shell=True)
        print("✅ npm dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install npm dependencies: {e}")
        return False

def build_python_backend():
    """Build the Python Flask backend"""
    print("\n🐍 Building Python backend...")
    try:
        # Run the Python build script
        subprocess.check_call([sys.executable, 'build_exe.py'])
        print("✅ Python backend built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build Python backend: {e}")
        return False

def create_electron_assets():
    """Create necessary assets for Electron"""
    assets_dir = 'electron/assets'
    os.makedirs(assets_dir, exist_ok=True)
    
    # Create a simple icon (you should replace with actual icons)
    icon_content = '''
    This folder should contain:
    - icon.ico (Windows icon, 256x256)
    - icon.icns (macOS icon)
    - icon.png (Linux icon, 512x512)
    - tray-icon.png (System tray icon, 16x16 or 32x32)
    
    For now, the application will use default Electron icons.
    '''
    
    with open(os.path.join(assets_dir, 'README.txt'), 'w') as f:
        f.write(icon_content)
    
    print("📁 Created assets directory structure")

def build_electron_app():
    """Build the Electron application"""
    print("\n⚡ Building Electron application...")
    
    # Build for current platform
    try:
        subprocess.check_call(['npm', 'run', 'dist'], shell=True)
        print("✅ Electron application built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build Electron application: {e}")
        return False

def create_portable_version():
    """Create a portable version without installer"""
    print("\n📦 Creating portable version...")
    try:
        subprocess.check_call(['npm', 'run', 'pack'], shell=True)
        print("✅ Portable version created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create portable version: {e}")
        return False

def setup_development_environment():
    """Setup development environment"""
    print("\n🔧 Setting up development environment...")
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        env_content = '''# Development Environment Variables
ELECTRON_IS_DEV=true
FLASK_ENV=development
FLASK_DEBUG=true
'''
        with open('.env', 'w') as f:
            f.write(env_content)
        print("📝 Created .env file")
    
    # Create electron-builder configuration if needed
    package_json_path = 'package.json'
    if os.path.exists(package_json_path):
        with open(package_json_path, 'r') as f:
            package_data = json.load(f)
        
        # Ensure build configuration exists
        if 'build' not in package_data:
            print("⚠️  Build configuration missing in package.json")
            return False
    
    return True

def main():
    """Main build function"""
    print("🚀 Abu Alaa Stores CRM - Electron Build Script")
    print("=" * 60)
    
    # Check prerequisites
    if not check_node_npm():
        input("Press Enter to exit...")
        return False
    
    # Setup development environment
    if not setup_development_environment():
        print("❌ Failed to setup development environment")
        input("Press Enter to exit...")
        return False
    
    # Install npm dependencies
    if not install_dependencies():
        input("Press Enter to exit...")
        return False
    
    # Create assets
    create_electron_assets()
    
    # Build Python backend first
    if not build_python_backend():
        print("⚠️  Python backend build failed, but continuing with Electron build...")
    
    # Ask user what to build
    print("\n🤔 What would you like to build?")
    print("1. Development version (quick)")
    print("2. Portable version (no installer)")
    print("3. Full installer (recommended)")
    print("4. All versions")
    
    choice = input("Enter your choice (1-4): ").strip()
    
    success = False
    
    if choice == '1':
        print("\n🔧 Starting development version...")
        try:
            subprocess.check_call(['npm', 'run', 'electron-dev'], shell=True)
            success = True
        except subprocess.CalledProcessError:
            print("❌ Development version failed to start")
    
    elif choice == '2':
        success = create_portable_version()
    
    elif choice == '3':
        success = build_electron_app()
    
    elif choice == '4':
        print("\n🏗️  Building all versions...")
        portable_success = create_portable_version()
        installer_success = build_electron_app()
        success = portable_success or installer_success
    
    else:
        print("❌ Invalid choice")
        input("Press Enter to exit...")
        return False
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("🎉 Build completed successfully!")
        print("\n📁 Output locations:")
        if os.path.exists('dist_electron'):
            print(f"  - Electron builds: {os.path.abspath('dist_electron')}")
        if os.path.exists('dist'):
            print(f"  - Python builds: {os.path.abspath('dist')}")
        
        print("\n📋 Next Steps:")
        print("1. Test the application on target systems")
        print("2. Create proper application icons")
        print("3. Sign the application for distribution (optional)")
        print("4. Create installation documentation")
        
    else:
        print("💥 Build failed!")
        print("\n🔧 Troubleshooting:")
        print("1. Check that all dependencies are installed")
        print("2. Ensure Python backend builds successfully")
        print("3. Check the console output for specific errors")
        print("4. Try building components individually")
    
    input("\nPress Enter to exit...")
    return success

if __name__ == "__main__":
    main()
