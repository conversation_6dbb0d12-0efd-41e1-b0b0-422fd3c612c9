using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System;
using System.Windows.Forms;
using CRMDesktopApp.Forms;
using CRMDesktopApp.Services;
using CRMDesktopApp.Data;

namespace CRMDesktopApp
{
    internal static class Program
    {
        public static ServiceProvider? ServiceProvider { get; private set; }
        public static IConfiguration? Configuration { get; private set; }

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configure application
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Setup configuration
            SetupConfiguration();

            // Setup dependency injection
            SetupServices();

            // Setup exception handling
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            try
            {
                // Initialize database
                var dbService = ServiceProvider?.GetService<IDatabaseService>();
                dbService?.InitializeDatabase();

                // Start the main form
                var mainForm = ServiceProvider?.GetService<MainForm>();
                if (mainForm != null)
                {
                    Application.Run(mainForm);
                }
                else
                {
                    MessageBox.Show("Failed to initialize application. Please check your installation.", 
                                  "Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Application startup failed: {ex.Message}", 
                              "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ServiceProvider?.Dispose();
            }
        }

        private static void SetupConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            Configuration = builder.Build();
        }

        private static void SetupServices()
        {
            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton(Configuration!);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add application services
            services.AddSingleton<IDatabaseService, DatabaseService>();
            services.AddSingleton<ICustomerService, CustomerService>();
            services.AddSingleton<ITransactionService, TransactionService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IAuthenticationService, AuthenticationService>();

            // Add forms
            services.AddTransient<MainForm>();
            services.AddTransient<LoginForm>();
            services.AddTransient<CustomerForm>();
            services.AddTransient<TransactionForm>();
            services.AddTransient<ReportsForm>();

            ServiceProvider = services.BuildServiceProvider();
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            var logger = ServiceProvider?.GetService<ILogger<Program>>();
            logger?.LogError(e.Exception, "Unhandled thread exception occurred");

            MessageBox.Show($"An unexpected error occurred: {e.Exception.Message}\n\nThe application will continue running.", 
                          "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var logger = ServiceProvider?.GetService<ILogger<Program>>();
            logger?.LogCritical((Exception)e.ExceptionObject, "Unhandled domain exception occurred");

            MessageBox.Show($"A critical error occurred: {((Exception)e.ExceptionObject).Message}\n\nThe application will now exit.", 
                          "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
