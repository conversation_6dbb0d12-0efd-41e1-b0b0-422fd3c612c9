using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using System;
using System.Windows.Forms;
using CRMDesktopApp.Forms;
using CRMDesktopApp.Services;
using CRMDesktopApp.Data;
using CRMDesktopApp.Models;

namespace CRMDesktopApp
{
    internal static class Program
    {
        public static ServiceProvider? ServiceProvider { get; private set; }
        public static IConfiguration? Configuration { get; private set; }

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configure application
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Setup configuration
            SetupConfiguration();

            // Setup dependency injection
            SetupServices();

            // Setup exception handling
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            try
            {
                // Show splash screen while initializing
                using (var splash = new SplashForm())
                {
                    splash.Show();
                    Application.DoEvents();

                    // Initialize database
                    var dbService = ServiceProvider?.GetService<IDatabaseService>();
                    if (dbService != null)
                    {
                        splash.UpdateStatus("تهيئة قاعدة البيانات...");
                        var initResult = dbService.InitializeDatabaseAsync().Result;
                        if (!initResult)
                        {
                            MessageBox.Show("فشل في تهيئة قاعدة البيانات", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }

                    splash.UpdateStatus("تحميل واجهة تسجيل الدخول...");
                    Application.DoEvents();

                    splash.Close();
                }

                // Show login form
                var loginForm = ServiceProvider?.GetService<LoginForm>();
                if (loginForm?.ShowDialog() == DialogResult.OK && loginForm.LoggedInUser != null)
                {
                    // Start the main application
                    var mainForm = new MainForm(
                        ServiceProvider.GetService<IDatabaseService>()!,
                        ServiceProvider,
                        loginForm.LoggedInUser
                    );

                    Application.Run(mainForm);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في بدء التطبيق: {ex.Message}",
                              "خطأ حرج", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ServiceProvider?.Dispose();
            }
        }

        private static void SetupConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            Configuration = builder.Build();
        }

        private static void SetupServices()
        {
            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton(Configuration!);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add Entity Framework
            services.AddDbContext<CRMDbContext>(options =>
            {
                // Database path will be configured in CRMDbContext
                options.UseSqlite();
            });

            // Add application services
            services.AddScoped<IDatabaseService, DatabaseService>();

            // Add forms
            services.AddTransient<LoginForm>();
            services.AddTransient<CustomersForm>();

            ServiceProvider = services.BuildServiceProvider();
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            var logger = ServiceProvider?.GetService<ILogger<Program>>();
            logger?.LogError(e.Exception, "Unhandled thread exception occurred");

            MessageBox.Show($"An unexpected error occurred: {e.Exception.Message}\n\nThe application will continue running.", 
                          "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var logger = ServiceProvider?.GetService<ILogger<Program>>();
            logger?.LogCritical((Exception)e.ExceptionObject, "Unhandled domain exception occurred");

            MessageBox.Show($"A critical error occurred: {((Exception)e.ExceptionObject).Message}\n\nThe application will now exit.", 
                          "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
