# محلات أبو علاء - نظام إدارة الزبائن

نظام إدارة الزبائن والمبيعات والتسديدات لمحلات أبو علاء، مبني بلغة Python مع واجهة عربية كاملة.

## المميزات

- ✅ إدارة الزبائن (إضافة، تعديل، حذف، عرض)
- ✅ المعاملات (بيع وتسديد)
- ✅ تتبع الأرصدة والمدفوعات
- ✅ التقارير مع تصدير Excel
- ✅ واجهة عربية كاملة (RTL)
- ✅ الوضع المظلم
- ✅ متوافق مع الهواتف المحمولة
- ✅ تطبيق ويب (PWA)
- ✅ ملف تنفيذي للويندوز (EXE)
- ✅ تطبيق الأندرويد (APK)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
python app.py
```

- **الرابط المحلي:** http://127.0.0.1:5000
- **الرابط الشبكي:** http://************:5000

### 3. بيانات الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## بناء ملف تنفيذي للويندوز (EXE)

### الطريقة الأولى: باستخدام السكريبت

```bash
python build_exe.py
```

### الطريقة الثانية: يدوياً

```bash
# تثبيت PyInstaller
pip install pyinstaller

# بناء الملف التنفيذي
pyinstaller --onefile --windowed --name="محلات_أبو_علاء_CRM" --add-data="templates;templates" --add-data="static;static" --add-data="uploads;uploads" app.py
```

### النتيجة
سيتم إنشاء ملف `محلات_أبو_علاء_CRM.exe` في مجلد `build_exe/`

## بناء تطبيق الأندرويد (APK)

### المتطلبات الأساسية

1. **Python 3.7+**
2. **Kivy**
3. **Buildozer**
4. **Android SDK**
5. **Android NDK**

### التثبيت

```bash
# تثبيت Kivy
pip install kivy

# تثبيت Buildozer
pip install buildozer

# تثبيت Cython
pip install cython
```

### بناء التطبيق

```bash
# إنشاء ملف buildozer.spec
buildozer init

# بناء التطبيق
buildozer android debug
```

### النتيجة
سيتم إنشاء ملف APK في مجلد `bin/`

## هيكل المشروع

```
محلات_أبو_علاء_CRM/
├── app.py                 # التطبيق الرئيسي (Flask)
├── android_app.py         # تطبيق الأندرويد (Kivy)
├── build_exe.py          # سكريبت بناء EXE
├── build_apk.py          # سكريبت بناء APK
├── requirements.txt      # متطلبات Python
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── dashboard.html
│   ├── customers.html
│   ├── add_customer.html
│   └── reports.html
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── app.js
│   ├── manifest.json
│   └── sw.js
└── uploads/              # مجلد رفع الملفات
```

## المميزات التقنية

### الواجهة الأمامية
- **HTML5/CSS3** مع تصميم متجاوب
- **JavaScript** للتفاعل
- **Bootstrap** للتصميم
- **Font Awesome** للأيقونات

### الخلفية
- **Flask** إطار العمل
- **SQLAlchemy** قاعدة البيانات
- **SQLite** قاعدة البيانات المحلية
- **Flask-Login** إدارة المستخدمين

### تطبيق الأندرويد
- **Kivy** إطار العمل
- **SQLite** قاعدة البيانات المحلية
- **واجهة عربية** كاملة

## الأمان

- تشفير كلمات المرور
- حماية من CSRF
- التحقق من المدخلات
- إدارة الجلسات الآمنة

## الدعم

تم تطوير هذا النظام بواسطة **خالد شجاع**

### معلومات التواصل
- **البريد الإلكتروني:** [البريد الإلكتروني]
- **الهاتف:** [رقم الهاتف]

## الترخيص

جميع الحقوق محفوظة © 2024 محلات أبو علاء

---

## ملاحظات مهمة

### للويندوز (EXE)
- تأكد من وجود مجلد `uploads` في نفس مجلد الملف التنفيذي
- سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- الملف التنفيذي يحتوي على جميع المتطلبات

### للأندرويد (APK)
- يحتاج إلى أذونات الإنترنت والتخزين
- يعمل على الأندرويد 5.0+ (API 21+)
- قاعدة البيانات محلية على الجهاز

### النسخ الاحتياطية
- احتفظ بنسخة احتياطية من قاعدة البيانات
- يمكن تصدير البيانات إلى Excel
- النسخ الاحتياطية التلقائية متاحة

---

**محلات أبو علاء** - نظام إدارة الزبائن المتكامل 🏪 